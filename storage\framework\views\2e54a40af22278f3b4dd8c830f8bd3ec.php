
<?php $__env->startSection('mytitle' ,'Class Report'); ?>
<?php $__env->startSection("css"); ?>

    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" type="text/css"
          rel="stylesheet"/>
    <link href="https://cdn.datatables.net/1.10.22/css/dataTables.bootstrap.min.css" type="text/css" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.4.1/semantic.min.css" type="text/css"
          rel="stylesheet"/>
    <link href="https://cdn.datatables.net/responsive/2.2.6/css/responsive.semanticui.min.css" type="text/css"
          rel="stylesheet"/>
    <link href="https://netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.min.css" rel="stylesheet"
          type="text/css"/>
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/dropdown.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/search.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/icon.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/grid.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/placeholder.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/transition.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/divider.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/button.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/card.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/popup.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/checkbox.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/form.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/table.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/modal.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/image.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/list.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/semantic/components/progress.min.css')); ?>">
    <link href="https://cdn.datatables.net/1.10.22/css/dataTables.bootstrap.min.css" type="text/css" rel="stylesheet"/>

    <link href="https://cdn.datatables.net/buttons/2.3.2/css/buttons.dataTables.min.css" type="text/css"
          rel="stylesheet"/>
    <link href="https://cdn.datatables.net/buttons/1.6.5/css/buttons.bootstrap.min.css" type="text/css"
          rel="stylesheet"/>


    <link href="https://cdn.datatables.net/responsive/2.2.6/css/responsive.bootstrap.min.css" type="text/css"
          rel="stylesheet"/>



    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr@latest/dist/plugins/monthSelect/style.css">
    <link href="<?php echo e(asset('/assets/workplace/hound/vendors/morris.js/morris.css')); ?>" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.9.0/fullcalendar.css"/>




    <style>
        /* Enhanced Base Styles with Smooth Transitions */
        table.small-font {
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .table-cell {
            white-space: nowrap;
        }

        .separator {
            margin: 0 0.25em;
        }

        .green-td {
            background-color: green;
            transition: background-color 0.3s ease;
        }

        .hefz-dropdown {
            color: white;
            border: none;
            background-color: green;
            transition: all 0.3s ease;
        }

        .classReportTabs {
            color: #337ab7 !important;
            transition: color 0.3s ease, transform 0.2s ease;
        }

        .classReportTabs:hover {
            color: #23527c !important;
            transform: translateY(-1px);
        }

        .report-item {
            border: 1px solid #f1f1f1;
            height: 150px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .report-item:hover {
            border-color: #ddd;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 🎯 ENHANCED PROGRESS BAR SYSTEM - APPLE-LEVEL DESIGN */

        /* Progress Bar Container */
        .enhanced-progress-container {
            position: relative;
            width: 100%;
            min-width: 120px;
            height: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .enhanced-progress-container:hover {
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        /* Dynamic Progress Bar Fill */
        .enhanced-progress-fill {
            height: 100%;
            border-radius: 10px;
            position: relative;
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            background: linear-gradient(135deg, var(--progress-color-start), var(--progress-color-end));
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* Shimmer Animation */
        .enhanced-progress-fill::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
            border-radius: 10px;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Progress Text Overlay */
        .enhanced-progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 11px;
            font-weight: 600;
            color: #1f2937;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
            z-index: 10;
            pointer-events: none;
            transition: all 0.3s ease;
        }

        /* Dynamic Color System */
        .progress-excellent {
            --progress-color-start: #10b981;
            --progress-color-end: #059669;
        }

        .progress-good {
            --progress-color-start: #f59e0b;
            --progress-color-end: #d97706;
        }

        .progress-poor {
            --progress-color-start: #ef4444;
            --progress-color-end: #dc2626;
        }

        .progress-zero {
            --progress-color-start: #9ca3af;
            --progress-color-end: #6b7280;
        }

        /* Hover Tooltips */
        .enhanced-progress-container[data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
            white-space: nowrap;
            z-index: 1000;
            margin-bottom: 5px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .enhanced-progress-container[data-tooltip]:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        /* Progress Legend */
        .progress-legend {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
            padding: 12px 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            font-weight: 500;
            color: #374151;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 6px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .legend-excellent { background: linear-gradient(135deg, #10b981, #059669); }
        .legend-good { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .legend-poor { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .legend-zero { background: linear-gradient(135deg, #9ca3af, #6b7280); }

        /* Enhanced Progress Bar Click Cursor */
        .enhanced-progress-container {
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .enhanced-progress-container:hover {
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(59, 130, 246, 0.3);
            transform: translateY(-2px) scale(1.02);
        }

        .enhanced-progress-container:active {
            transform: translateY(0) scale(0.98);
        }

        /* 🎯 ENHANCED DATATABLES STYLING */

        /* DataTable Container */
        .dataTables_wrapper {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }

        .dataTables_wrapper:hover {
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        /* Enhanced Table Styling */
        .table-enhanced {
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
        }

        .table-enhanced thead th {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: #ffffff;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 16px 12px;
            border: none;
            position: relative;
            transition: all 0.3s ease;
        }

        .table-enhanced thead th:hover {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            transform: translateY(-1px);
        }

        .table-enhanced tbody td {
            padding: 14px 12px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
            transition: all 0.3s ease;
            font-size: 13px;
        }

        .table-enhanced tbody tr {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .table-enhanced tbody tr:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .table-enhanced tbody tr:nth-child(even) {
            background: rgba(248, 250, 252, 0.5);
        }

        .table-enhanced tbody tr:nth-child(even):hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(16, 185, 129, 0.08) 100%);
        }

        /* Enhanced Pagination */
        .dataTables_paginate {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
        }

        .dataTables_paginate .paginate_button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
            padding: 0 12px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .dataTables_paginate .paginate_button:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: #ffffff;
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .dataTables_paginate .paginate_button.current {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: #ffffff;
            border-color: #10b981;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .dataTables_paginate .paginate_button.disabled {
            background: #f1f5f9;
            color: #9ca3af;
            border-color: #e2e8f0;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .dataTables_paginate .paginate_button.disabled:hover {
            background: #f1f5f9;
            color: #9ca3af;
            transform: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        /* DataTable Info and Length */
        .dataTables_info {
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            margin-top: 16px;
        }

        .dataTables_length select {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 6px 12px;
            color: #374151;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .dataTables_length select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Search Input Enhancement */
        .dataTables_filter input {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px 16px;
            color: #374151;
            font-size: 14px;
            transition: all 0.3s ease;
            width: 250px;
        }

        .dataTables_filter input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .dataTables_filter input::placeholder {
            color: #9ca3af;
        }

        /* Loading Overlay */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 12px;
            backdrop-filter: blur(4px);
        }

        .loading-spinner {
            text-align: center;
            color: #3b82f6;
        }

        .loading-text {
            margin-top: 12px;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
        }

        /* Row Click Effects */
        .table-enhanced tbody tr.row-clicked {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(16, 185, 129, 0.15) 100%) !important;
            transform: scale(1.01);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* Sorting Animation */
        .table-enhanced thead th.sorting-active {
            background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Responsive Enhancements */
        @media (max-width: 768px) {
            .dataTables_wrapper {
                padding: 12px;
            }

            .table-enhanced thead th {
                padding: 12px 8px;
                font-size: 12px;
            }

            .table-enhanced tbody td {
                padding: 10px 8px;
                font-size: 12px;
            }

            .dataTables_paginate .paginate_button {
                min-width: 35px;
                height: 35px;
                padding: 0 8px;
                font-size: 13px;
            }

            .dataTables_filter input {
                width: 200px;
            }
        }

        /* 🎯 SWEETALERT CUSTOM STYLING */
        .calculation-details-popup {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .calculation-details-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .calculation-details-content {
            font-size: 14px;
            line-height: 1.6;
        }

        .calculation-details-content code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }

        .calculation-details-content h4 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: 600;
        }

        .calculation-details-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .calculation-details-content li {
            margin-bottom: 4px;
        }

        .stAttendanceth {
            padding: .5rem !important;
            font-size: 10px !important;
        }

        .stAttendancetd {
            padding: .3rem !important;
            font-size: 9px !important;
        }

        #classReportTable.table > tbody > tr > td {
            padding: 5px;
        }

        .fc-scroller {
            overflow-x: visible !Important;
        }

        html, body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.6;
        }

        #calendar {
            max-width: 1100px;
            margin: 40px auto;
        }

        /* 🎨 MODERN FILTER SYSTEM - COMPREHENSIVE STYLES WITH OVERRIDE PROTECTION */
        .modern-filter-system {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
            border-radius: 16px !important;
            padding: 24px !important;
            margin-bottom: 24px !important;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06) !important;
            border: 1px solid rgba(226, 232, 240, 0.8) !important;
            position: relative !important;
            overflow: hidden !important;
            z-index: 100 !important;
        }

        /* Select2 Override Protection - Clean Professional Styling */
        .modern-filter-system .select2-container {
            z-index: 1000 !important;
        }

        .modern-filter-system .select2-container .select2-selection {
            border: 2px solid #e2e8f0 !important;
            border-radius: 8px !important;
            background: #ffffff !important;
            min-height: 42px !important;
        }

        .modern-filter-system .select2-container .select2-selection:focus,
        .modern-filter-system .select2-container .select2-selection:hover {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        .modern-filter-system .select2-dropdown {
            border: 2px solid #e2e8f0 !important;
            border-radius: 8px !important;
            z-index: 1001 !important;
        }

        /* Fix Awful Green Chips - Professional Selected Items */
        .modern-filter-system .select2-selection__choice {
            background: #f1f5f9 !important;
            border: 1px solid #cbd5e1 !important;
            border-radius: 6px !important;
            color: #475569 !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            padding: 4px 8px !important;
            margin: 2px !important;
        }

        .modern-filter-system .select2-selection__choice__remove {
            color: #64748b !important;
            font-weight: bold !important;
            margin-right: 6px !important;
        }

        .modern-filter-system .select2-selection__choice__remove:hover {
            color: #dc2626 !important;
        }

        /* Remove Green Container Background Initially */
        .modern-filter-system .filter-group {
            background: transparent !important;
        }

        /* Prevent Initial Green Background on Filter Cards */
        .filter-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
        }

        /* Only apply green background when user actually selects something */
        .filter-card.user-active {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.15), 0 8px 25px rgba(16, 185, 129, 0.2) !important;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%) !important;
        }

        .modern-filter-system::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444);
            background-size: 300% 100%;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Filter Header */
        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }

        .filter-title h4 {
            margin: 0;
            color: #1e293b;
            font-weight: 700;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-title .fa {
            color: #3b82f6;
            font-size: 18px;
        }

        .filter-subtitle {
            margin: 4px 0 0 0;
            color: #64748b;
            font-size: 14px;
            font-weight: 400;
        }

        .filter-progress {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .progress-bar {
            width: 120px;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #10b981);
            border-radius: 4px;
            width: 0%;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            min-width: 80px;
        }

        /* Filter Grid */
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        /* Filter Cards - Apple-inspired Design */
        .filter-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            border: 2px solid #e2e8f0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .filter-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669);
            transform: scaleX(0);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .filter-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15), 0 8px 16px rgba(59, 130, 246, 0.08);
            transform: translateY(-4px) scale(1.02);
        }

        .filter-card:hover::before {
            background: linear-gradient(90deg, #3b82f6, #10b981);
            transform: scaleX(1);
        }

        .filter-card.active {
            border-color: #10b981;
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.15), 0 8px 25px rgba(16, 185, 129, 0.2);
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
        }

        .filter-card.active::before {
            background: #10b981;
            transform: scaleX(1);
        }

        .filter-card.active:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2), 0 20px 40px rgba(16, 185, 129, 0.25);
        }

        .filter-card.error {
            border-color: #ef4444;
            box-shadow: 0 8px 32px rgba(239, 68, 68, 0.15);
        }

        .filter-card.error::before {
            background: #ef4444;
        }

        /* Filter Card Header */
        .filter-card-header {
            display: flex;
            align-items: center;
            padding: 16px 20px 12px;
            gap: 12px;
        }

        .filter-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .filter-icon .fa {
            font-size: 16px;
            color: #64748b;
            transition: all 0.3s ease;
        }

        .filter-card:hover .filter-icon {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
        }

        .filter-card:hover .filter-icon .fa {
            color: #3b82f6;
            transform: scale(1.1);
        }

        .filter-card.active .filter-icon {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
        }

        .filter-card.active .filter-icon .fa {
            color: #10b981;
        }

        .filter-label {
            flex: 1;
        }

        .filter-label label {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
            margin: 0;
            display: block;
        }

        .required-indicator {
            color: #ef4444;
            font-size: 12px;
            font-weight: 700;
            margin-left: 4px;
        }

        .optional-indicator {
            color: #64748b;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-status {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .filter-status .fa {
            font-size: 14px;
            color: #cbd5e1;
            transition: all 0.3s ease;
        }

        .filter-card.active .filter-status .fa {
            color: #10b981;
        }

        .filter-card.active .filter-status .fa-circle-o::before {
            content: '\f058'; /* fa-check-circle */
        }

        /* Filter Card Body */
        .filter-card-body {
            padding: 0 20px 20px;
        }

        .modern-select {
            width: 100%;
            padding: 14px 18px;
            border: 2px solid #e2e8f0;
            border-radius: 14px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 16px center;
            background-repeat: no-repeat;
            background-size: 18px;
            padding-right: 45px;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        .modern-select:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.15), 0 8px 25px rgba(16, 185, 129, 0.1);
            transform: scale(1.02);
            background: white;
        }

        .modern-select:hover {
            border-color: #3b82f6;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
            transform: translateY(-2px);
            background: white;
        }

        .modern-select:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .modern-select:disabled:hover {
            border-color: #e2e8f0;
            box-shadow: none;
            transform: none;
        }

        /* CRITICAL FIX: Student Filter with Proper Z-Index and Container Management */
        .student-filter-card {
            position: relative;
            z-index: 1;
            overflow: visible;
        }

        .student-filter-card.active {
            z-index: 999;
        }

        .student-filter-card.active::before {
            z-index: -1;
        }

        .student-filter-card.active .filter-status .fa {
            color: #10b981;
        }

        /* 🎨 SIDE DRAWER PANEL - COMPREHENSIVE IMPLEMENTATION */
        
        /* Panel Overlay */
        .student-panel-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            backdrop-filter: blur(4px);
        }

        .student-panel-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Main Panel Container */
        .student-selection-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 380px;
            height: 100vh;
            background: white;
            box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .student-selection-panel.open {
            right: 0;
        }

        /* Panel Header */
        .student-panel-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            flex-shrink: 0;
        }

        .student-panel-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .student-panel-title h4 {
            font-size: 22px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            margin-top: 8px;
        }

        .student-panel-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #64748b;
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .student-panel-close:hover {
            background: #f8fafc;
            color: #1e293b;
        }

        .student-panel-subtitle {
            color: #64748b;
            font-size: 14px;
            margin: 0;
        }

        /* Panel Body */
        .student-panel-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
            background: #fafbfc;
        }

        /* Search Box */
        .student-search-box {
            position: relative;
            margin-bottom: 20px;
        }

        .student-search-input {
            width: 100%;
            padding: 12px 45px 12px 40px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s ease;
        }

        .student-search-input:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .student-search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
            font-size: 14px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .student-search-icon:hover {
            color: #10b981;
        }

        .student-search-clear {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
            font-size: 14px;
            cursor: pointer;
            display: none;
            transition: color 0.2s ease;
        }

        .student-search-clear:hover {
            color: #ef4444;
        }

        /* Filter Options */
        .student-filter-options {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .student-filter-option {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .student-filter-option:hover {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .student-filter-option.active {
            background: #dcfce7;
            border-color: #a7f3d0;
            color: #166534;
        }

        /* Student List */
        .student-panel-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .student-panel-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            object-fit: cover;
            border: 2px solid #e2e8f0;
            transition: border-color 0.2s ease;
        }

        .student-panel-item:hover .student-avatar {
            border-color: #10b981;
        }

        .student-panel-item.selected .student-avatar {
            border-color: #10b981;
        }

        /* Search highlighting */
        .search-highlight {
            background-color: #fef08a;
            color: #854d0e;
            font-weight: 600;
            padding: 1px 2px;
            border-radius: 2px;
        }

        .student-panel-item:hover {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .student-panel-item.selected {
            border-color: #10b981;
            background: #f0fdf4;
            position: relative;
        }

        .student-panel-item.selected::after {
            content: "✓";
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #10b981;
            font-weight: bold;
            font-size: 14px;
        }

        .student-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #cbd5e1;
            border-radius: 4px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.2s ease;
        }

        .student-panel-item.selected .student-checkbox {
            border-color: #10b981;
            background: #10b981;
        }

        .student-checkbox::after {
            content: "";
            display: none;
            width: 8px;
            height: 4px;
            border: 2px solid white;
            border-top: none;
            border-right: none;
            transform: rotate(-45deg);
        }

        .student-panel-item.selected .student-checkbox::after {
            display: block;
        }

        .student-info {
            flex: 1;
        }

        .student-name {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 3px;
            font-size: 14px;
        }

        .student-meta {
            font-size: 12px;
            color: #64748b;
        }

        /* Panel Summary */
        .student-panel-summary {
            padding: 16px 20px;
            background: white;
            border-top: 1px solid #e2e8f0;
            flex-shrink: 0;
        }

        .student-summary-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .student-selected-count {
            font-weight: 500;
            color: #1e293b;
            font-size: 14px;
        }

        .student-selected-count.highlight {
            color: #10b981;
        }

        .student-action-buttons {
            display: flex;
            gap: 12px;
        }

        .student-btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .student-btn-outline {
            background: white;
            border: 1px solid #e2e8f0;
            color: #475569;
        }

        .student-btn-outline:hover {
            background: #f8fafc;
            border-color: #cbd5e1;
        }

        .student-btn-primary {
            background: #10b981;
            color: white;
        }

        .student-btn-primary:hover {
            background: #0da272;
        }

        /* Empty State */
        .student-empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #94a3b8;
        }

        .student-empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
            color: #cbd5e1;
        }

        .student-empty-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: #475569;
            font-size: 16px;
        }

        .student-empty-description {
            font-size: 14px;
            color: #64748b;
            max-width: 280px;
            margin: 0 auto;
        }

        /* Loading State */
        .student-loading-state {
            text-align: center;
            padding: 40px 20px;
        }

        .student-loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #10b981;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .student-loading-text {
            color: #64748b;
            font-size: 14px;
        }

        /* Student Filter Card Updates */
        .student-filter-card {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .student-filter-card:hover {
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            border-color: #b6f6e8;
        }

        .student-count-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: #dcfce7;
            color: #166534;
            border-radius: 20px;
            padding: 2px 10px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
            min-width: 24px;
        }

        .student-count-badge.has-selection {
            background: #10b981;
            color: white;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .student-selection-panel {
                width: 100%;
                right: -100%;
            }
            
            .student-panel-header {
                padding: 16px;
            }
            
            .student-panel-body {
                padding: 16px;
            }
            
            .student-panel-item {
                padding: 16px 12px;
            }
            
            .student-name {
                font-size: 15px;
            }
            
            .student-meta {
                font-size: 13px;
            }
            
            .student-checkbox {
                width: 24px;
                height: 24px;
                margin-right: 16px;
            }
        }

        /* Scroll Performance */
        .student-panel-body {
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 transparent;
        }

        .student-panel-body::-webkit-scrollbar {
            width: 6px;
        }

        .student-panel-body::-webkit-scrollbar-track {
            background: transparent;
        }

        .student-panel-body::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .student-panel-body::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* CRITICAL FIX: Self-contained wrapper with proper z-index */
        .student-list-wrapper {
            position: relative;
            z-index: 1000; /* CRITICAL: Above all other elements */
            min-height: 40px; /* CRITICAL: Always display selections */
            overflow: visible; /* CRITICAL: Allow Select2 dropdown to extend */
        }

        .student-select-container {
            position: relative;
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .students-select {
            width: 100%;
            min-height: 48px;
            padding-right: 45px; /* Space for clear icon */
        }

        /* CRITICAL FIX: Clear Icon inside field (matching other filters) */
        .select-clear-icon {
            position: absolute;
            right: 35px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1001; /* Above Select2 */
        }

        .select-clear-icon:hover {
            background: #ef4444;
            color: white;
        }

        .select-clear-icon .fa {
            font-size: 10px;
            color: #64748b;
        }

        .select-clear-icon:hover .fa {
            color: white;
        }

        /* CRITICAL FIX: Internal Search/Filter */
        .student-search-container {
            margin-bottom: 12px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .student-search-input {
            width: 100%;
            padding: 8px 35px 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 13px;
            background: #f8fafc;
            transition: all 0.3s ease;
        }

        .student-search-input:focus {
            outline: none;
            border-color: #10b981;
            background: white;
        }

        .search-icon {
            position: absolute;
            right: 12px;
            color: #64748b;
            font-size: 12px;
        }

        /* CRITICAL FIX: Dynamic Selection Display with proper height */
        .student-selection-display {
            min-height: 40px; /* CRITICAL: Always visible */
            max-height: 200px; /* CRITICAL: Prevent excessive height */
            overflow-y: auto; /* CRITICAL: Scrollable when needed */
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            position: relative;
            z-index: 1;
        }

        /* CRITICAL FIX: Contextual header directly above list */
        .selection-header {
            padding: 8px 12px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 2;
        }

        .selection-context {
            display: block;
        }

        /* CRITICAL FIX: Selected students list with proper scrolling */
        .selected-students-list {
            padding: 8px;
            min-height: 32px; /* Ensure minimum height */
        }

        .selected-student-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 8px;
            margin-bottom: 4px;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            font-size: 13px;
            color: #065f46;
            transition: all 0.3s ease;
        }

        .selected-student-item:last-child {
            margin-bottom: 0;
        }

        .selected-student-item:hover {
            background: #dcfce7;
            border-color: #86efac;
        }

        .student-name {
            font-weight: 500;
        }

        .remove-student-btn {
            width: 16px;
            height: 16px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .remove-student-btn:hover {
            background: #dc2626;
            transform: scale(1.1);
        }

        /* Loading State */
        .student-loading {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            color: #64748b;
            font-size: 13px;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dots span {
            width: 6px;
            height: 6px;
            background: #10b981;
            border-radius: 50%;
            animation: loadingDots 1.4s ease-in-out infinite both;
        }

        .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
        .loading-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loadingDots {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .student-filter-card {
                grid-column: 1;
            }

            .student-selection-info {
                padding: 10px 12px;
                font-size: 12px;
            }

            .selection-summary {
                margin-bottom: 6px;
            }
        }

        @media (max-width: 480px) {
            .filter-grid {
                grid-template-columns: 1fr;
            }

            .students-select {
                min-height: 44px;
                font-size: 14px;
            }

            .select-clear-icon {
                right: 30px;
                width: 18px;
                height: 18px;
            }

            /* CRITICAL FIX: Mobile optimizations for student list */
            .student-selection-display {
                max-height: 150px; /* Smaller on mobile */
            }

            .selected-student-item {
                padding: 8px;
                font-size: 12px;
            }

            .remove-student-btn {
                width: 20px;
                height: 20px;
                font-size: 11px;
            }

            .student-search-input {
                padding: 10px 35px 10px 12px;
                font-size: 14px;
            }

            .selection-header {
                padding: 10px 12px;
                font-size: 11px;
            }
        }

        /* Generation feedback indicator */
        .generation-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 2px solid #bfdbfe;
            border-radius: 12px;
            color: #1e40af;
            font-size: 14px;
            font-weight: 500;
            margin-left: 16px;
            animation: slideInRight 0.3s ease-out;
        }

        .generation-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #bfdbfe;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* CRITICAL FIX: Select2 Z-Index Override */
        .select2-container {
            z-index: 1001 !important;
        }

        .select2-dropdown {
            z-index: 1002 !important;
            border: 2px solid #10b981 !important;
            border-radius: 8px !important;
            box-shadow: 0 8px 32px rgba(16, 185, 129, 0.15) !important;
        }

        .select2-results__option {
            padding: 8px 12px !important;
            font-size: 13px !important;
        }

        .select2-results__option--highlighted {
            background-color: #f0fdf4 !important;
            color: #065f46 !important;
        }

        .select2-results__option[data-select-all="true"] {
            background: #f0fdf4 !important;
            color: #10b981 !important;
            font-weight: bold !important;
            border-bottom: 1px solid #bbf7d0 !important;
        }

        /* Ensure Select2 dropdown appears above everything */
        .student-list-wrapper .select2-container--open .select2-dropdown {
            z-index: 1003 !important;
        }
        }

        .student-count-badge {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
            animation: bounceIn 0.5s ease;
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .student-quick-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #e2e8f0;
        }

        .quick-action-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .quick-action-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            background: #f8fafc;
            transform: translateY(-1px);
        }

        .quick-action-btn.select-all:hover {
            border-color: #10b981;
            color: #10b981;
        }

        .quick-action-btn.clear-selection:hover {
            border-color: #ef4444;
            color: #ef4444;
        }

        /* Selected Students Summary */
        .selected-students-summary {
            margin-top: 12px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .summary-count {
            font-size: 13px;
            font-weight: 600;
            color: #475569;
        }

        .toggle-details {
            background: none;
            border: none;
            color: #64748b;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .toggle-details:hover {
            background: #e2e8f0;
            color: #3b82f6;
        }

        .toggle-details .fa {
            transition: transform 0.3s ease;
        }

        .toggle-details.expanded .fa {
            transform: rotate(180deg);
        }

        .summary-details {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #e2e8f0;
        }

        .selected-students-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .student-tag {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
            animation: slideInUp 0.3s ease;
        }

        @keyframes slideInUp {
            from { transform: translateY(10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .student-tag .remove-student {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.2s ease;
        }

        .student-tag .remove-student:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* Action Bar */
        .filter-action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0 0;
            border-top: 2px solid #e2e8f0;
            margin-top: 8px;
        }

        .action-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .action-btn {
            padding: 14px 28px;
            border-radius: 16px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            letter-spacing: 0.025em;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn:active {
            transform: scale(0.98);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
        }

        .action-btn.primary:hover:not(:disabled) {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .action-btn.primary:disabled {
            background: #e2e8f0;
            color: #94a3b8;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            color: #64748b;
            border: 2px solid #e2e8f0;
            backdrop-filter: blur(10px);
        }

        .action-btn.secondary:hover:not(:disabled) {
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 30px rgba(59, 130, 246, 0.15);
            background: white;
        }

        .action-btn.secondary:disabled {
            background: #f8fafc;
            color: #cbd5e1;
            border-color: #f1f5f9;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .filter-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .filter-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .filter-progress {
                align-self: stretch;
                justify-content: space-between;
            }

            .filter-action-bar {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .action-group {
                justify-content: center;
            }

            .student-filter-card {
                grid-column: 1;
            }
        }

        @media (max-width: 480px) {
            .modern-filter-system {
                padding: 16px;
                margin-bottom: 16px;
            }

            .filter-card-header {
                padding: 12px 16px 8px;
            }

            .filter-card-body {
                padding: 0 16px 16px;
            }

            .action-btn {
                padding: 10px 20px;
                font-size: 13px;
            }
        }

        /* Loading States */
        .filter-card.loading {
            opacity: 0.7;
            pointer-events: none;
            position: relative;
        }

        .filter-card.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 10;
        }

        /* Multi-Class Selection Enhancements */
        .modern-select-multi {
            width: 100%;
            border: 2px solid #e2e8f0;
            border-radius: 14px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .filter-hint {
            display: block;
            font-size: 11px;
            color: #64748b;
            margin-top: 2px;
            font-weight: 400;
        }

        .multi-class-warning, .multi-center-warning {
            margin-top: 12px;
            padding: 10px 12px;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 8px;
            animation: slideDown 0.3s ease;
        }

        .warning-content {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #92400e;
        }

        .warning-content .fa {
            color: #f59e0b;
            font-size: 14px;
        }

        .class-selection-summary, .center-selection-summary {
            margin-top: 12px;
            padding: 10px 12px;
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border: 1px solid #10b981;
            border-radius: 8px;
            animation: slideDown 0.3s ease;
        }

        .summary-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 13px;
            color: #166534;
            font-weight: 500;
        }

        .selected-count {
            font-weight: 600;
            color: #10b981;
        }

        .total-students {
            font-size: 12px;
            color: #059669;
        }

        /* Select2 Multi-Select Styling */
        .select2-container--default .select2-selection--multiple {
            border: 2px solid #e2e8f0 !important;
            border-radius: 14px !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
            min-height: 48px !important;
            padding: 6px 12px !important;
        }

        .select2-container--default .select2-selection--multiple:focus {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
            border: none !important;
            border-radius: 8px !important;
            color: white !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            padding: 4px 8px !important;
            margin: 2px 4px 2px 0 !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: rgba(255, 255, 255, 0.8) !important;
            font-weight: bold !important;
            margin-right: 6px !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: white !important;
            background: rgba(255, 255, 255, 0.2) !important;
            border-radius: 50% !important;
        }

        .select2-dropdown {
            border: 2px solid #e2e8f0 !important;
            border-radius: 12px !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%) !important;
            color: #166534 !important;
        }

        /* Accessibility Enhancements */
        .filter-card:focus-within {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .action-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        /* Animation Classes - Apple-inspired */
        .fade-in {
            animation: fadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .slide-up {
            animation: slideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .bounce-in {
            animation: bounceIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .pulse-loading {
            animation: pulseLoading 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
        }

        .shimmer {
            animation: shimmer 2s linear infinite;
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3) rotate(-10deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.1) rotate(5deg);
            }
            70% {
                transform: scale(0.95) rotate(-2deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes pulseLoading {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.05);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }



        /*
        i wish this required CSS was better documented :(
        https://github.com/FezVrasta/popper.js/issues/674
        derived from this CSS on this page: https://popper.js.org/tooltip-examples.html
        */

        .popper,
        .tooltip {
            position: absolute;
            z-index: 9999;
            background: #FFC107;
            color: black;
            width: 150px;
            border-radius: 3px;
            box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
            padding: 10px;
            text-align: center;
        }

        .style5 .tooltip {
            background: #1E252B;
            color: #FFFFFF;
            max-width: 200px;
            width: auto;
            font-size: .8rem;
            padding: .5em 1em;
        }

        .popper .popper__arrow,
        .tooltip .tooltip-arrow {
            width: 0;
            height: 0;
            border-style: solid;
            position: absolute;
            margin: 5px;
        }

        .tooltip .tooltip-arrow,
        .popper .popper__arrow {
            border-color: #FFC107;
        }

        .style5 .tooltip .tooltip-arrow {
            border-color: #1E252B;
        }

        .popper[x-placement^="top"],
        .tooltip[x-placement^="top"] {
            margin-bottom: 5px;
        }

        .popper[x-placement^="top"] .popper__arrow,
        .tooltip[x-placement^="top"] .tooltip-arrow {
            border-width: 5px 5px 0 5px;
            border-left-color: transparent;
            border-right-color: transparent;
            border-bottom-color: transparent;
            bottom: -5px;
            left: calc(50% - 5px);
            margin-top: 0;
            margin-bottom: 0;
        }

        .popper[x-placement^="bottom"],
        .tooltip[x-placement^="bottom"] {
            margin-top: 5px;
        }

        .tooltip[x-placement^="bottom"] .tooltip-arrow,
        .popper[x-placement^="bottom"] .popper__arrow {
            border-width: 0 5px 5px 5px;
            border-left-color: transparent;
            border-right-color: transparent;
            border-top-color: transparent;
            top: -5px;
            left: calc(50% - 5px);
            margin-top: 0;
            margin-bottom: 0;
        }

        .tooltip[x-placement^="right"],
        .popper[x-placement^="right"] {
            margin-left: 5px;
        }

        .popper[x-placement^="right"] .popper__arrow,
        .tooltip[x-placement^="right"] .tooltip-arrow {
            border-width: 5px 5px 5px 0;
            border-left-color: transparent;
            border-top-color: transparent;
            border-bottom-color: transparent;
            left: -5px;
            top: calc(50% - 5px);
            margin-left: 0;
            margin-right: 0;
        }

        .popper[x-placement^="left"],
        .tooltip[x-placement^="left"] {
            margin-right: 5px;
        }

        .popper[x-placement^="left"] .popper__arrow,
        .tooltip[x-placement^="left"] .tooltip-arrow {
            border-width: 5px 0 5px 5px;
            border-top-color: transparent;
            border-right-color: transparent;
            border-bottom-color: transparent;
            right: -5px;
            top: calc(50% - 5px);
            margin-left: 0;
            margin-right: 0;
        }

        table.monthlyStudentReportTable tbody td {
            border: 1px solid black;
            color: black;
        }

        table.monthlyStudentReportTable thead tr th {
            font-weight: bold;
        }

        a.studentName {
            color: black; /* set the desired text color */
            text-decoration: none; /* remove the default underline style */
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th {
            font-weight: bold;
            text-align: center;
        }

        thead tr {
            text-align: center;
        }


        .tableColumnHeadingNormalFont {
            /*font-weight: normal;*/
            /*font-size: 12px;*/
            text-transform: capitalize !important;
        }

        #MonthlyHalaqahReportDatatable,
        #MonthlyHalaqahReportDatatable th,
        #MonthlyHalaqahReportDatatable td {
            border: 1px solid rgba(31, 255, 15, 0.471) !important;
        }


        #monthlyStudentRevisionReportTable,
        #monthlyStudentRevisionReportTable th,
        #monthlyStudentRevisionReportTable td {
            border: 1px solid rgba(31, 255, 15, 0.471) !important;
        }

        #halaqahSummaryTable,
        #halaqahSummaryTable th,
        #halaqahSummaryTable td {
            border: 1px solid rgba(31, 255, 15, 0.471) !important;
        }

        #MonthlyHalaqahReportDatatable tbody tr:hover {
            background-color: rgba(31, 255, 15, 0.168) !important;
        }


        #monthlyStudentRevisionReportTable tbody tr:hover {
            background-color: rgba(31, 255, 15, 0.168) !important;
        }

        #halaqahSummaryTable tbody tr:hover {
            background-color: rgba(31, 255, 15, 0.168) !important;
        }


    </style>
    <style>
        .progress {
            display: flex;
            align-items: center;
            max-width: 500px;
            overflow: hidden;
        }

        .progress-bar {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>

    <style>


        .dropdown-container {
            position: relative;
            border: 2px solid transparent;
            border-radius: 5px; /* Adjust the value as per your preference */
        }

        @keyframes border-colors {
            0% {
                border-color: transparent;
            }
            25% {
                border-color: #28a745;
            }
            50% {
                border-color: #17a2b8;
            }
            75% {
                border-color: #ffc107;
            }
            100% {
                border-color: #dc3545;
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        .loading {
            animation: border-colors 2s linear infinite;
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #28a745;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 1000;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced DataTable Styles */
        .dataTables_wrapper {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .dataTables_wrapper:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .dataTables_processing {
            background: rgba(255,255,255,0.95) !important;
            border: none !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
            color: #28a745 !important;
            font-weight: 600 !important;
            font-size: 16px !important;
        }

        .dataTables_processing::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-left: 10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #28a745;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Enhanced Table Styles */
        .table-enhanced {
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .table-enhanced thead th {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            padding: 15px 12px;
            position: relative;
            transition: all 0.3s ease;
        }

        .table-enhanced thead th:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        }

        .table-enhanced tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #f8f9fa;
        }

        .table-enhanced tbody tr:hover {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(32, 201, 151, 0.05) 100%);
            transform: scale(1.01);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table-enhanced tbody td {
            padding: 12px;
            border: none;
            vertical-align: middle;
            transition: all 0.2s ease;
        }

        /* Skeleton Loading Animation */
        .skeleton-loading {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200px 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
            height: 20px;
            margin: 5px 0;
        }

        .skeleton-row {
            display: flex;
            gap: 10px;
            padding: 10px;
            align-items: center;
        }

        .skeleton-row .skeleton-loading:nth-child(1) { width: 5%; }
        .skeleton-row .skeleton-loading:nth-child(2) { width: 25%; }
        .skeleton-row .skeleton-loading:nth-child(3) { width: 20%; }
        .skeleton-row .skeleton-loading:nth-child(4) { width: 20%; }
        .skeleton-row .skeleton-loading:nth-child(5) { width: 15%; }
        .skeleton-row .skeleton-loading:nth-child(6) { width: 10%; }
        .skeleton-row .skeleton-loading:nth-child(7) { width: 5%; }

        /* Enhanced loading states */
        .filter-loading {
            position: relative;
            opacity: 0.6;
            pointer-events: none;
        }

        .filter-loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 1000;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Empty state styling */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            background: #f9f9f9;
            border-radius: 5px;
            margin: 20px 0;
        }

        .empty-state i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
        }

        .empty-state h4 {
            color: #999;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #666;
            margin-bottom: 20px;
        }

        /* Error state styling */
        .error-state {
            text-align: center;
            padding: 30px 20px;
            color: #d9534f;
            background: #f2dede;
            border: 1px solid #ebccd1;
            border-radius: 5px;
            margin: 20px 0;
        }

        .error-state i {
            font-size: 36px;
            margin-bottom: 15px;
        }

        .retry-button {
            background: #d9534f;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        .retry-button:hover {
            background: #c9302c;
        }

        /* Filter reset button styling */
        .filter-controls {
            margin-bottom: 20px;
            text-align: right;
        }

        .clear-filters-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .clear-filters-btn:hover {
            background: #5a6268;
        }

        .clear-filters-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }


        .dataTable td {
            word-wrap: break-word;
            white-space: normal;
        }

        .lowercase {
            text-transform: lowercase;
        }

        .class-link {
            color: #b4eeb0;
            padding: 2px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .class-link:hover {
            background-color: #1fff0f !important;
            color: #000 !important; /* Text color set to black and forced */
            text-decoration: underline !important;
            border-radius: 4px !important;
        }


    </style>

    <link rel="stylesheet" type="text/css" href="<?php echo e(url('css/print-styles.css')); ?>" media="print">

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    

    <div class="ui right floated green">
        <div class="ui big breadcrumb " style="background: transparent">
            <a class="section" href="<?php echo e(url('workplace')); ?>">Dashboard</a>
            <i class="right chevron icon divider"></i>

            <a target="_blank" class="section" href="#">Educational Report</a>
            
            <i class="right chevron icon divider"></i>

            <div class="active section">Class Reporting Module</div>
        </div>
    </div>


    <div class="panel-heading"><a class="section classDetailsUrl" target="_blank"
                                  href="#">Class Details
            <i class="external alternate icon"></i></a></div>

    <div class="panel-body">
        <ul role="tablist" class="nav nav-tabs" id="myTabs">

            <li role="presentation">
                <a class="classReportTabs " aria-expanded="true" data-toggle="tab" role="tab" id="home_tab"
                   href="<?php echo e(route('educationalreports.student')); ?>">Student Report</a>
            </li>
            <li class="active" role="presentation">
                <a class="classReportTabs" aria-expanded="true" data-toggle="tab" role="tab" id="home_tab"
                   href="<?php echo e(route('educationalreports.class')); ?>">Class Report</a>
            </li>
            <li class="" role="presentation">
                <a class="classReportTabs" aria-expanded="true" data-toggle="tab" role="tab" id="home_tab"
                   href="<?php echo e(route('educationalreports.center')); ?>">
                    Center Report</a>
            </li>
            <li role="presentation">
                <a class="classReportTabs" aria-expanded="true" data-toggle="tab" role="tab" id="home_tab"
                   href="<?php echo e(route('educationalreports.itqan')); ?>">Itqan
                    Report</a>
            </li>


        </ul>
        <div class="tab-content table-bordered" id="myTabContent">
            <div id="<?php echo e(route('reports.create', ['id' => \App\Classes::has('students')->first()->id, 'from_date' => \Carbon\Carbon::now()->toDateString()])); ?>"
                 class="tab-pane fade active in" role="tabpanel">
                <div class="panel-body">
                    <br>


                    <!-- 🎨 MODERN FILTER SYSTEM - COMPREHENSIVE REDESIGN -->
                    <div class="modern-filter-system">
                        <?php
                            $selectedCenterId = request('centerId');
                            $selectedClassId = request('classId');
                            $selectedMonthYear = request('monthYear');
                        ?>

                        <!-- Filter Header with Progress Indicator -->
                        <div class="filter-header">
                            <div class="filter-title">
                                <h4><i class="fa fa-filter"></i> Report Filters</h4>
                                <p class="filter-subtitle">Configure your report parameters</p>
                            </div>

                        </div>

                        <!-- Main Filter Grid -->
                        <div class="filter-grid">
                            <!-- Center Selection Card -->
                            <div class="filter-card" data-filter="center">
                                <div class="filter-card-header">
                                    <div class="filter-icon">
                                        <i class="fa fa-building"></i>
                                    </div>
                                    <div class="filter-label">
                                        <label>Center</label>
                                        <span class="required-indicator">*</span>
                                    </div>
                                    <div class="filter-status" id="centerStatus">
                                        <i class="fa fa-circle-o"></i>
                                    </div>
                                </div>
                                <div class="filter-card-body">
                                    <?php
                                        $centers = \App\Center::whereHas('classes.students.hefz', function ($query) {
                                            $query->whereNotNull('hefz_from_surat')
                                                  ->whereNotNull('hefz_from_ayat')
                                                  ->whereNotNull('hefz_to_surat')
                                                  ->whereNotNull('hefz_to_ayat');
                                        })
                                        ->withCount('classes')
                                        ->orderBy('location')
                                        ->get();
                                    ?>
                                    <select name="monthlyHalaqahReportCenter" class="modern-select"
                                            id="monthlyHalaqahReportCenterList" multiple>
                                        <?php $__currentLoopData = $centers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $center): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option title="<?php echo e($center->id); ?>" data-name="<?php echo e($center->name); ?>"
                                                    value="<?php echo e($center->id); ?>"
                                                    <?php echo e($selectedCenterId == $center->id ? 'selected' : ''); ?>>
                                                <?php echo e($center->name); ?> (<?php echo e($center->classes_count); ?> classes)
                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>

                                    <!-- Multi-Center Warning -->
                                    <div class="multi-center-warning" id="centerWarning" style="display: none;">
                                        <div class="warning-content">
                                            <i class="fa fa-exclamation-triangle"></i>
                                            <span>Large export detected! This may take several minutes.</span>
                                        </div>
                                    </div>

                                    <!-- Center Selection Summary -->
                                    <div class="center-selection-summary" id="centerSummary" style="display: none;">
                                        <div class="summary-content">
                                            <i class="fa fa-check-circle"></i>
                                            <span id="centerSummaryText">0 centers selected</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Class Selection Card - Enhanced Multi-Select -->
                            <div class="filter-card" data-filter="class">
                                <div class="filter-card-header">
                                    <div class="filter-icon">
                                        <i class="fa fa-users"></i>
                                    </div>
                                    <div class="filter-label">
                                        <label>Classes</label>
                                        <span class="required-indicator">*</span>
                                        <small class="filter-hint">Select up to 5 classes</small>
                                    </div>
                                    <div class="filter-status" id="classStatus">
                                        <i class="fa fa-circle-o"></i>
                                    </div>
                                </div>
                                <div class="filter-card-body">
                                    <?php
                                        $classes = \App\Classes::whereHas('students.hefz', function ($query) {
                                            $query->whereNotNull('hefz_from_surat')
                                                  ->whereNotNull('hefz_from_ayat')
                                                  ->whereNotNull('hefz_to_surat')
                                                  ->whereNotNull('hefz_to_ayat');
                                        })->with('programs')
                                        ->withCount('students')
                                        ->get();
                                    ?>
                                    <select name="classesList[]" data-custom-attribute="" class="modern-select-multi"
                                            id="classesList" multiple="multiple" data-placeholder="Choose classes...">
                                        <?php $__currentLoopData = $classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option data-programTitle="<?php echo e($class->programs->first()->title); ?>"
                                                    title="<?php echo e($class->id); ?>" data-name="<?php echo e($class->name); ?>"
                                                    value="<?php echo e($class->id); ?>"
                                                    <?php echo e($selectedClassId == $class->id ? 'selected' : ''); ?>>
                                                <?php echo e($class->name); ?> (<?php echo e($class->students_count); ?> students)
                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>

                                    <!-- Multi-Class Selection Warning -->
                                    <div class="multi-class-warning" id="multiClassWarning" style="display: none;">
                                        <div class="warning-content">
                                            <i class="fa fa-exclamation-triangle"></i>
                                            <span class="warning-text">Large export detected. This may take several minutes.</span>
                                        </div>
                                    </div>

                                    <!-- Class Selection Summary -->
                                    <div class="class-selection-summary" id="classSelectionSummary" style="display: none;">
                                        <div class="summary-content">
                                            <span class="selected-count">0</span> classes selected
                                            <span class="total-students" id="totalStudentsCount"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Month-Year Selection Card -->
                            <div class="filter-card" data-filter="monthYear">
                                <div class="filter-card-header">
                                    <div class="filter-icon">
                                        <i class="fa fa-calendar"></i>
                                    </div>
                                    <div class="filter-label">
                                        <label>Period</label>
                                        <span class="required-indicator">*</span>
                                    </div>
                                    <div class="filter-status" id="monthYearStatus">
                                        <i class="fa fa-circle-o"></i>
                                    </div>
                                </div>
                                <div class="filter-card-body">
                                    <select name="monthlyHalaqahReportMonthYear" class="modern-select"
                                            id="monthlyHalaqahReportMonthYearList">
                                        <option value="">Choose period...</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Student Selection Card - Side Drawer Trigger -->
                            <div class="filter-card student-filter-card" data-filter="students" id="openStudentPanel">
                                <div class="filter-card-header">
                                    <div class="filter-icon">
                                        <i class="fa fa-users"></i>
                                    </div>
                                    <div class="filter-label">
                                        <label>Students</label>
                                        <span class="optional-indicator">Optional</span>
                                    </div>
                                    <div class="filter-status" id="studentStatus">
                                        <i class="fa fa-circle-o"></i>
                                    </div>
                                </div>
                                <div class="filter-card-body">
                                    <div class="filter-value" id="studentFilterValue">
                                        <span id="studentSelectionText">Select students</span>
                                        <span class="student-count-badge" id="studentCountBadge">0</span>
                                    </div>

                                    <!-- Hidden student list for JavaScript compatibility -->
                                    <select name="studentList" id="studentList" multiple style="display: none;">
                                        <option value="">Choose students...</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Action Bar -->
                        <!-- Action Bar -->
                        <div class="filter-action-bar">
                            <div class="action-group secondary-actions">
                                <button type="button" id="clearFiltersBtn" class="action-btn secondary" disabled>
                                    <i class="fa fa-refresh"></i>
                                    <span>Reset All</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="filter-controls">
                                <div id="filterValidationMessage" class="alert alert-warning" style="display: none; margin-bottom: 10px;">
                                    <i class="fa fa-exclamation-triangle"></i>
                                    <span id="filterValidationText"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <input type="hidden" id="baseUrl"
                                   value="<?php echo e(route('class-tables-pdf',['classId'=>'CLASS_ID','centerId'=>'CENTER_ID','monthYear'=>'MONTH_YEAR'])); ?>">

                            <div style="margin-top: 27px;">
                                <a style="display: none;" target="_blank" id="printButton" href=""
                                   class="btn btn-primary">Print all Tables</a>
                                <!-- Export to Excel button positioned right next to Print button -->
                                <a style="display: none;margin-left: 10px;" id="exportExcelButton" href="#"
                                   class="btn btn-success"><i class="fa fa-file-excel-o"></i> Export to Excel</a>
                            </div>
                        </div>

                        <input type="hidden" id="supervisorInfo" name="supervisorInfo" value="">

                    </div>

                    <br>
                 

                    <div id="otherTables" style="display:none;">

                    <div class="container-fluid">

                        <div id="hef-intro">
                            <br>
                            <p>Memorization Table</p>
                        </div>
                        <div class="MonthlyHalaqahReportTableLoader">

                            <table class="small-font ui inverted table  table-bordered  MonthlyHalaqahReportDatatable"
                                   id="MonthlyHalaqahReportDatatable" style="width:100%">
                                <thead style="background-color: rgba(31,255,15,0.168); font-weight: bold;">
                                <tr style="font-weight: bold;">
                                    <th class="tableColumnHeadingNormalFont"
                                        style="border:1px solid black; width: 2% !important;">ID
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important; border:1px solid black;">Student
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important; border:1px solid black;">Memorization
                                        Plan
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 13% !important;border:1px solid black;">Memorization
                                        Report
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important;border:1px solid black;">Total Memorized
                                        Pages
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important;border:1px solid black;">Attendance %
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important;border:1px solid black;">Achievement %
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- Modal -->
                        <div class="modal fade" id="reportModal" tabindex="-1" role="dialog"
                             aria-labelledby="reportModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="classModalLabel">Class Details</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <!-- List of classes will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br>
                    <div class="container-fluid">
                        <div id="revision-intro">
                            <p>Revision Table</p>
                        </div>

                        <div class="monthlyStudentRevisionReportTableLoader">

                            <table class="small-font ui inverted table  table-bordered   monthlyStudentRevisionReportTable"
                                   id="monthlyStudentRevisionReportTable" style="width:100%">
                                <thead style="background-color: rgba(31,255,15,0.168);; font-weight: bold;">
                                <tr style="font-weight: bold;">
                                    <th class="tableColumnHeadingNormalFont"
                                        style="border:1px solid black; width: 2% !important;">ID
                                    </th>

                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important; !important; border:1px solid black;">Student
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important;border:1px solid black;">Revision Plan
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important;border:1px solid black;">Revision
                                        Report
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important;border:1px solid black;">Total Revised
                                        Pages
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important;border:1px solid black;">Attendance %
                                    </th>
                                    <th class="tableColumnHeadingNormalFont"
                                        style="width: 14% !important;border:1px solid black;">Achievement %
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <!-- Table body content goes here -->
                                </tbody>
                            </table>


                        </div>
                    </div>
                    <br>
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-xs-12">
                                <h6 class="text-left">Summary <span class="lowercase">of</span> <span
                                            class="summaryClassName"></span> Class – <span class="lowercase">of</span>
                                    <span class="summaryCenterName"></span> Center Achievement Report <span
                                            class="lowercase">for</span> Month: <span class="summaryMonth"></span></h6>
                            </div>
                        </div>
                        <br>
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="halaqahSummaryTableLoader">

                                <table id="halaqahSummaryTable"
                                       class="table ui inverted small-font table  table-bordered"
                                       style="width:100%">
                                    <thead style="background-color: rgba(31,255,15,0.168); font-weight: bold;">
                                    <tr>
                                        <th class="tableColumnHeadingNormalFont" style="border:1px solid black;">No of
                                            Students
                                        </th>
                                        <th class="tableColumnHeadingNormalFont" style="border:1px solid black;">Total
                                            Memorized Pages
                                        </th>
                                        <th class="tableColumnHeadingNormalFont" style="border:1px solid black;">Total
                                            Revised Pages
                                        </th>
                                        <th class="tableColumnHeadingNormalFont" style="border:1px solid black;">
                                            Attendance %
                                        </th>
                                        <th class="tableColumnHeadingNormalFont" style="border:1px solid black;">
                                            Achievement %
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <!-- add your table data here -->
                                    </tbody>
                                </table>
                            </div>
                            </div>
                        </div>
                        <br><br><br>
                    </div>


                    </div>
                </div>
            </div>
        </div>
    </div>


    <?php


        //        dd(asset("uploads/settings/logo.png"));
                    $logo = asset("uploads/settings/logo.png");
                $letterHead = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");

    ?>
<?php $__env->stopSection(); ?>
    <?php $__env->startSection('js'); ?>


        <script src="<?php echo e(asset('js/semantic/semantic.min.js')); ?>"></script>
        <script src="<?php echo e(asset('js/semantic/components/dropdown.js')); ?>"></script>
        <script src="<?php echo e(asset('js/semantic/components/search.js')); ?>"></script>
        <script src="<?php echo e(asset('js/semantic/components/transition.js')); ?>"></script>
        <script src="<?php echo e(asset('js/semantic/components/progress.min.js')); ?>"></script>
        <script src="<?php echo e(asset('js/semantic/components/form.min.js')); ?>"></script>

        <script src="https://cdn.jsdelivr.net/npm/flatpickr@latest/dist/plugins/monthSelect/index.js"></script>

        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>


        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
        <script src="https://fullcalendar.io/js/fullcalendar-2.1.1/lib/jquery-ui.custom.min.js"></script>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.9.0/fullcalendar.js"></script>

        <script src="<?php echo e(asset('assets/workplace/hound/js/datatables/dataTables.buttons.min.js')); ?>"></script>
        <script src="<?php echo e(asset('assets/workplace/hound/js/datatables/buttons.bootstrap.min.js')); ?>"></script>
        <script src="<?php echo e(asset('assets/workplace/hound/js/datatables/buttons.colVis.min.js')); ?>"></script>
        <script src="<?php echo e(asset('assets/workplace/hound/js/datatables/buttons.flash.min.js')); ?>"></script>
        <script src="<?php echo e(asset('assets/workplace/hound/js/datatables/buttons.html5.min.js')); ?>"></script>
        <script src="<?php echo e(asset('assets/workplace/hound/js/datatables/buttons.print.min.js')); ?>"></script>
        <script src="<?php echo e(asset('assets/workplace/hound/js/datatables/dataTables.responsive.min.js')); ?>"></script>
        <script src="<?php echo e(asset('assets/workplace/hound/js/datatables/responsive.bootstrap.min.js')); ?>"></script>
        <script src="https://cdn.datatables.net/select/1.4.0/js/dataTables.select.min.js"></script>
        <?php echo $__env->make('jssnippets.select2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php echo $__env->make('jssnippets.flatpickr', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <script>
        // ===================================================================
        // GLOBAL HALAQAH REPORT SYSTEM DEFINITION
        // ===================================================================

        // Define the global system object first to avoid reference errors
        var HalaqahReportSystem = {
            // State management
            state: {
                activeRequests: new Map(),
                filterValues: {
                    center: '',
                    class: '',
                    monthYear: '',
                    students: []
                },
                dataTableInstances: new Map(),
                isInitialized: false,
                // ULTRATHINKING: Prevent double AJAX calls with intelligent debouncing
                lastGenerationTime: 0,
                generationDebounceMs: 500,
                isGenerating: false
            },

            // Configuration
            config: {
                debounceDelay: 300,
                loadingTimeout: 30000,
                animationDuration: 300,
                routes: {
                    halaqahReport: "<?php echo e(route('educationalreports.halaqah-report')); ?>",
                    revisionReport: "<?php echo e(route('educationalreports.class-wise-student-revision-achievement-report')); ?>",
                    summaryReport: "<?php echo e(route('educationalreports.month.end.halaqah.summary.report')); ?>",
                    centerClasses: "<?php echo e(url('workplace/educationalreports/center-classes/')); ?>",
                    classMonthYears: "<?php echo e(url('workplace/educationalreports/class-month-years/CLASS_ID')); ?>",
                    classStudents: "<?php echo e(route('educationalreports.halaqah-students-with-records', ['classId' => 'CLASS_ID'])); ?>",
                    centerSupervisor: "<?php echo e(url('workplace/educationalreports/center-supervisor/')); ?>",
                    getTeachers: "<?php echo e(route('educationalreports.get.teachers', ':classId')); ?>",
                    exportExcel: "<?php echo e(route('educationalreports.export-halaqah-excel', ['classId' => 'CLASS_ID'])); ?>",
                    printTables: "<?php echo e(route('class-tables-pdf',['classId'=>'CLASS_ID','centerId'=>'CENTER_ID','monthYear'=>'MONTH_YEAR'])); ?>"
                }
            }
        };

        // ===================================================================
        // MODERN FILTER SYSTEM INITIALIZATION
        // ===================================================================

        HalaqahReportSystem.init = function() {
            console.log('🎨 Initializing Modern Filter System...');

            // Initialize Select2 for modern selects
            this.initializeSelect2();

            // Bind event handlers
            this.bindEventHandlers();

            // Initialize filter checking
            this.filters.checkFiltersAndGenerate();

            console.log('✅ Modern Filter System initialized successfully');
        };

        HalaqahReportSystem.initializeSelect2 = function() {
            // Initialize multi-center select
            $('#monthlyHalaqahReportCenterList').select2({
                theme: 'default',
                width: '100%',
                placeholder: 'Choose centers...',
                allowClear: true,
                closeOnSelect: false,
                maximumSelectionLength: 5,
                minimumResultsForSearch: 3,
                templateResult: function(option) {
                    if (!option.id) return option.text;
                    return $('<span><i class="fa fa-building"></i> ' + option.text + '</span>');
                },
                templateSelection: function(option) {
                    if (!option.id) return option.text;
                    return $('<span><i class="fa fa-building"></i> ' + option.text + '</span>');
                }
            });

            // Initialize single selects (excluding multi-selects)
            $('.modern-select:not([multiple]):not(.student-multiselect)').select2({
                theme: 'default',
                width: '100%',
                placeholder: function() {
                    return $(this).find('option:first').text();
                },
                allowClear: false,
                minimumResultsForSearch: 5
            });

            // Initialize multi-class select with enhanced features
            $('.modern-select-multi').select2({
                theme: 'default',
                width: '100%',
                placeholder: 'Choose classes...',
                allowClear: true,
                closeOnSelect: false,
                maximumSelectionLength: 5,
                minimumResultsForSearch: 3,
                templateSelection: function(selection) {
                    if (selection.id === '') return selection.text;

                    // Extract class name without student count for cleaner display
                    const cleanName = selection.text.split(' (')[0];
                    return cleanName;
                },
                language: {
                    maximumSelected: function(args) {
                        return 'You can only select ' + args.maximum + ' classes maximum.';
                    }
                }
            });

            // SIDE DRAWER IMPLEMENTATION: Select2 for students dropdown is no longer needed
            // Student selection is now handled by the side drawer panel
            // $('.students-select').select2({ ... }) - REMOVED
        };

        // ===================================================================
        // ADVANCED REQUEST MANAGEMENT SYSTEM
        // ===================================================================

        HalaqahReportSystem.requestManager = {
            activeRequests: new Map(),
            requestQueue: new Map(),
            cache: new Map(),

            // Cancel specific request
            cancelRequest: function(key) {
                const request = this.activeRequests.get(key);
                if (request && request.readyState !== 4) {
                    request.abort();
                    this.activeRequests.delete(key);
                    console.log(`🚫 Cancelled request: ${key}`);
                }
            },

            // Cancel all requests
            cancelAllRequests: function() {
                this.activeRequests.forEach((request, key) => {
                    if (request.readyState !== 4) {
                        request.abort();
                        console.log(`🚫 Cancelled request: ${key}`);
                    }
                });
                this.activeRequests.clear();
            },

            // Smart request execution with caching and deduplication
            executeRequest: function(key, requestConfig) {
                // Cancel existing request for this key
                this.cancelRequest(key);

                // Check cache first
                const cacheKey = `${key}_${JSON.stringify(requestConfig.data || {})}`;
                if (this.cache.has(cacheKey)) {
                    console.log(`💾 Using cached data for: ${key}`);
                    const cachedData = this.cache.get(cacheKey);
                    if (requestConfig.success) {
                        requestConfig.success(cachedData);
                    }
                    return Promise.resolve(cachedData);
                }

                console.log(`🚀 Executing request: ${key}`);

                const request = $.ajax({
                    ...requestConfig,
                    success: (response) => {
                        this.activeRequests.delete(key);
                        // Cache successful responses
                        this.cache.set(cacheKey, response);
                        console.log(`✅ Request completed: ${key}`);
                        if (requestConfig.success) {
                            requestConfig.success(response);
                        }
                    },
                    error: (xhr, status, error) => {
                        this.activeRequests.delete(key);
                        console.error(`❌ Request failed: ${key}`, error);
                        if (requestConfig.error) {
                            requestConfig.error(xhr, status, error);
                        }
                    }
                });

                this.activeRequests.set(key, request);
                return request;
            }
        };

        HalaqahReportSystem.bindEventHandlers = function() {
            // Prevent multiple bindings
            if (HalaqahReportSystem.state.eventHandlersBound) {
                console.log('⚠️ Event handlers already bound, skipping...');
                return;
            }

            console.log('🔗 Binding event handlers (single instance)...');

            // Unbind any existing handlers to prevent duplicates
            $('#monthlyHalaqahReportCenterList, #classesList, #monthlyHalaqahReportMonthYearList, #studentList').off('change.halaqah');

            // Multi-Center selection handler
            $('#monthlyHalaqahReportCenterList').on('change.halaqah', function() {
                const centerIds = $(this).val() || [];
                console.log(`🏢 Centers changed: ${centerIds}`);

                HalaqahReportSystem.filters.updateState('center', centerIds);

                // Show/hide warning for multiple centers
                if (Array.isArray(centerIds) && centerIds.length >= 3) {
                    $('#centerWarning').slideDown(300);
                } else {
                    $('#centerWarning').slideUp(300);
                }

                // Update center summary
                HalaqahReportSystem.updateCenterSummary(centerIds);

                if (centerIds.length > 0) {
                    HalaqahReportSystem.loadMultiCenterClasses(centerIds);
                } else {
                    // Clear dependent dropdowns
                    HalaqahReportSystem.clearDependentDropdowns(['class', 'monthYear', 'students']);
                }
            });

            // Multi-Class selection handler
            $('#classesList').on('change.halaqah', function() {
                const selectedClasses = $(this).val() || [];
                console.log(`👥 Classes changed: ${selectedClasses.join(', ')}`);

                // Update multi-class selection UI
                HalaqahReportSystem.updateMultiClassSelection(selectedClasses);

                // Update state with array of class IDs
                HalaqahReportSystem.filters.updateState('class', selectedClasses);

                if (selectedClasses.length > 0) {
                    // Load month-years for the first selected class (or intersection of all)
                    HalaqahReportSystem.loadMultiClassMonthYears(selectedClasses);
                    HalaqahReportSystem.loadMultiClassStudents(selectedClasses);
                } else {
                    // Clear dependent dropdowns
                    HalaqahReportSystem.clearDependentDropdowns(['monthYear', 'students']);
                }
            });

            // Month-Year selection handler
            $('#monthlyHalaqahReportMonthYearList').on('change.halaqah', function() {
                const monthYear = $(this).val();
                console.log(`📅 Month-Year changed: ${monthYear}`);
                HalaqahReportSystem.filters.updateState('monthYear', monthYear);
            });

            // SIDE DRAWER IMPLEMENTATION: Student selection now handled by side drawer panel
            // Old dropdown-based student selection handler removed
            // $('#studentList').on('change.halaqah', ...) - REMOVED

            // SIDE DRAWER IMPLEMENTATION: Clear function now handled by side drawer panel
            // Old clear icon handler removed
            // $(document).on('click', '#studentClearIcon', ...) - REMOVED

            // CRITICAL FIX: Internal search functionality
            $(document).on('input', '#studentSearchInput', function() {
                const searchTerm = $(this).val().toLowerCase();
                const selectedStudentsList = $('#selectedStudentsList');

                if (searchTerm === '') {
                    // Show all selected students
                    selectedStudentsList.find('.selected-student-item').show();
                } else {
                    // Filter students by search term
                    selectedStudentsList.find('.selected-student-item').each(function() {
                        const studentName = $(this).find('.student-name').text().toLowerCase();
                        if (studentName.includes(searchTerm)) {
                            $(this).show();
                        } else {
                            $(this).hide();
                        }
                    });
                }

                console.log(`🔍 Filtering students by: "${searchTerm}"`);
            });



            // Clear filters handler
            $('#clearFiltersBtn').on('click', function() {
                HalaqahReportSystem.filters.clearAll();
            });

            // Apply filters handler (for manual trigger)
            $('#applyFiltersBtn').on('click', function() {
                if (HalaqahReportSystem.filters.validateRequired()) {
                    HalaqahReportSystem.dataTable.initializeAll();
                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-check"></i> Report generated successfully',
                        'success',
                        3000
                    );
                }
            });

            // Student details toggle handler
            $(document).on('click', '#toggleStudentDetails', function() {
                const details = $('#studentDetailsPanel');
                const icon = $(this).find('.fa');

                if (details.is(':visible')) {
                    details.slideUp(200);
                    icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                    $(this).removeClass('expanded');
                } else {
                    details.slideDown(200);
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                    $(this).addClass('expanded');
                }
            });

            // Remove individual student handler
            $(document).on('click', '.remove-student', function() {
                const studentId = $(this).data('student-id');
                const currentValues = $('#studentList').val() || [];
                const newValues = currentValues.filter(id => id !== studentId.toString());

                $('#studentList').val(newValues).trigger('change');
            });

            // Excel export handler
            $(document).on('click', '#exportExcelButton', function(e) {
                e.preventDefault();

                const state = HalaqahReportSystem.state.filterValues;

                if (!HalaqahReportSystem.filters.validateRequired()) {
                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-exclamation-triangle"></i> Please select all required filters first',
                        'warning',
                        4000
                    );
                    return;
                }

                console.log('Exporting Excel with filters:', state);

                // Build export URL for multi-class/center support
                let exportUrl;
                const isMultiCenter = Array.isArray(state.center) && state.center.length > 1;
                const isMultiClass = Array.isArray(state.class) && state.class.length > 1;

                if (isMultiCenter || isMultiClass) {
                    // Multi-center or multi-class export - use new route
                    exportUrl = '/workplace/educationalreports/export-halaqah-excel-multi';
                    exportUrl += `?monthYear=${encodeURIComponent(state.monthYear)}`;

                    // Add centers if multiple selected
                    if (isMultiCenter) {
                        exportUrl += `&centers=${state.center.join(',')}`;
                    } else if (state.center) {
                        exportUrl += `&centerId=${Array.isArray(state.center) ? state.center[0] : state.center}`;
                    }

                    // Add classes if multiple selected
                    if (isMultiClass) {
                        exportUrl += `&classes=${state.class.join(',')}`;
                    }
                } else {
                    // Single class export - use existing route for backward compatibility
                    const classId = Array.isArray(state.class) ? state.class[0] : state.class;
                    exportUrl = HalaqahReportSystem.config.routes.exportExcel.replace('CLASS_ID', classId);
                    exportUrl += `?monthYear=${encodeURIComponent(state.monthYear)}`;

                    if (state.center) {
                        exportUrl += `&centerId=${Array.isArray(state.center) ? state.center[0] : state.center}`;
                    }
                }

                if (state.students.length > 0) {
                    exportUrl += `&studentId=${state.students.join(',')}`;
                }

                console.log('Export URL:', exportUrl);

                // Show enhanced loading state with progress indicator
                const $exportBtn = $('#exportExcelButton');
                const originalHtml = $exportBtn.html();

                // Show progress modal for large exports
                const selectedClasses = $('#classesList').val() || [];
                const selectedCenters = $('#monthlyHalaqahReportCenterList').val() || [];
                const isLargeExport = (Array.isArray(selectedClasses) && selectedClasses.length >= 3) ||
                                     (Array.isArray(selectedCenters) && selectedCenters.length >= 2);

                if (isLargeExport) {
                    HalaqahReportSystem.ui.showExportProgressModal();
                }

                $exportBtn.html('<i class="fa fa-spinner fa-spin"></i> Exporting...').prop('disabled', true);

                // Direct download approach
                window.location.href = exportUrl;

                // Reset button after delay and hide progress modal
                setTimeout(() => {
                    $exportBtn.html(originalHtml).prop('disabled', false);

                    // Hide progress modal if it was shown
                    HalaqahReportSystem.ui.hideExportProgressModal();

                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-check"></i> Excel export initiated successfully',
                        'success',
                        3000
                    );
                }, 2000);
            });

            // ===================================================================
            // SIDE DRAWER EVENT HANDLERS
            // ===================================================================

            // Open student panel
            $(document).on('click', '#openStudentPanel', function() {
                HalaqahReportSystem.openStudentPanel();
            });

            // Close student panel
            $(document).on('click', '#closeStudentPanel, #studentPanelOverlay', function() {
                HalaqahReportSystem.closeStudentPanel();
            });

            // Student selection in panel
            $(document).on('click', '.student-panel-item', function() {
                const studentId = $(this).data('student-id').toString();
                HalaqahReportSystem.toggleStudentInPanel(studentId);
            });

            // Search students in panel
            $(document).on('input', '#studentPanelSearch', function() {
                const searchTerm = $(this).val();
                
                // Show/hide clear icon
                if (searchTerm.trim()) {
                    $('#studentSearchClear').show();
                } else {
                    $('#studentSearchClear').hide();
                }
                
                // Clear previous timeout
                if (HalaqahReportSystem.studentPanel.searchTimeout) {
                    clearTimeout(HalaqahReportSystem.studentPanel.searchTimeout);
                }
                
                // Debounce search
                HalaqahReportSystem.studentPanel.searchTimeout = setTimeout(() => {
                    HalaqahReportSystem.searchStudentsInPanel(searchTerm);
                }, 300);
            });

            // Clear search input
            $(document).on('click', '#studentSearchClear', function() {
                $('#studentPanelSearch').val('');
                $('#studentSearchClear').hide();
                HalaqahReportSystem.searchStudentsInPanel('');
            });

            // Handle search icon click
            $(document).on('click', '.student-search-icon', function() {
                $('#studentPanelSearch').focus();
            });

            // Apply student selection
            $(document).on('click', '#applyStudentSelection', function() {
                HalaqahReportSystem.applyStudentSelection();
            });

            // Clear student selection
            $(document).on('click', '#clearStudentSelection', function() {
                HalaqahReportSystem.clearStudentSelection();
            });

            // Prevent panel close when clicking inside
            $(document).on('click', '#studentPanel', function(e) {
                e.stopPropagation();
            });

            // Mark handlers as bound
            HalaqahReportSystem.state.eventHandlersBound = true;
        };

        // ===================================================================
        // UTILITY FUNCTIONS
        // ===================================================================

        HalaqahReportSystem.clearDependentDropdowns = function(dropdownTypes) {
            console.log(`🧹 Clearing dependent dropdowns: ${dropdownTypes.join(', ')}`);

            dropdownTypes.forEach(type => {
                switch(type) {
                    case 'class':
                        HalaqahReportSystem.filters.updateState('class', '');
                        HalaqahReportSystem.ui.updateDropdownOptions('#classesList', '<option value="">Choose a class...</option>');
                        break;
                    case 'monthYear':
                        HalaqahReportSystem.filters.updateState('monthYear', '');
                        HalaqahReportSystem.ui.updateDropdownOptions('#monthlyHalaqahReportMonthYearList', '<option value="">Choose period...</option>');
                        break;
                    case 'students':
                        HalaqahReportSystem.filters.updateState('students', []);
                        HalaqahReportSystem.ui.updateDropdownOptions('#studentList', '<option value="">Choose students...</option>');
                        break;
                }
            });
        };

        // ===================================================================
        // DOCUMENT READY INITIALIZATION
        // ===================================================================

        $(document).ready(function () {
            console.log('🚀 Document ready - Starting initialization...');

            // Initialize UI dropdown first
            $('.ui.dropdown').dropdown({
                clearable: true,
            });

            // Initialize modern filter system after DOM is ready
            setTimeout(function() {
                console.log('🔍 Checking HalaqahReportSystem availability...');
                if (typeof HalaqahReportSystem !== 'undefined' && HalaqahReportSystem.init) {
                    console.log('✅ HalaqahReportSystem found, initializing...');
                    HalaqahReportSystem.init();
                } else {
                    console.error('❌ HalaqahReportSystem is not properly defined');
                    console.log('Available:', typeof HalaqahReportSystem);
                    if (typeof HalaqahReportSystem !== 'undefined') {
                        console.log('Object keys:', Object.keys(HalaqahReportSystem));
                    }
                }
            }, 100);
        });

    </script>

    <script>
        // gender dropdown
        $('.ui.dropdown').dropdown();
        $("#studentId").dropdown({
            onChange: function (val) {
                console.log(val)
            }
        });


        // ===================================================================
        // ENHANCED DATA LOADING FUNCTIONS
        // ===================================================================

        // Update center selection summary
        HalaqahReportSystem.updateCenterSummary = function(centerIds) {
            const centerCount = Array.isArray(centerIds) ? centerIds.length : (centerIds ? 1 : 0);

            if (centerCount > 0) {
                $('#centerSummaryText').text(`${centerCount} center${centerCount > 1 ? 's' : ''} selected`);
                $('#centerSummary').slideDown(300);
            } else {
                $('#centerSummary').slideUp(300);
            }
        };

        // Load classes for multiple centers - SINGLE ROUTE CALL
        HalaqahReportSystem.loadMultiCenterClasses = function(centerIds) {
            console.log(`🏢 Loading classes for centers: ${centerIds}`);

            HalaqahReportSystem.ui.showDropdownLoading('#classesList');

            // Handle both single and multiple centers
            const centers = Array.isArray(centerIds) ? centerIds : [centerIds];

            // Single route call with multiple center IDs
            const url = '/workplace/educationalreports/center-classes/multi';
            const centerIdsParam = centers.join(',');

            $.get(url, { centerIds: centerIdsParam })
                .then(response => {
                    if (response.classes && response.classes.length > 0) {
                        // Build options from response
                        let options = '';
                        response.classes.forEach(cls => {
                            options += `<option value="${cls.id}" data-name="${cls.class_code}">${cls.class_code} (${cls.students_count || 0} students)</option>`;
                        });

                        HalaqahReportSystem.ui.updateDropdownOptions('#classesList', options);
                        console.log(`✅ Loaded ${response.classes.length} classes from ${centers.length} centers via single route`);
                    } else {
                        HalaqahReportSystem.ui.updateDropdownOptions('#classesList', '<option value="">No classes found</option>');
                        console.log(`⚠️ No classes found for centers: ${centers}`);
                    }
                })
                .catch(error => {
                    console.error('❌ Error loading multi-center classes:', error);
                    HalaqahReportSystem.ui.updateDropdownOptions('#classesList', '<option value="">Error loading classes</option>');
                })
                .finally(() => {
                    HalaqahReportSystem.ui.hideDropdownLoading('#classesList');
                });
        };

        HalaqahReportSystem.loadCenterClasses = function(centerId) {
            console.log(`🏢 Loading classes for center: ${centerId}`);

            HalaqahReportSystem.ui.showDropdownLoading('#classesList');

            const url = HalaqahReportSystem.config.routes.centerClasses + '/' + centerId;

            HalaqahReportSystem.requestManager.executeRequest('centerClasses', {
                url: url,
                method: 'GET',
                data: { centerId: centerId },
                success: function(response) {
                    let options = ''; // No placeholder for multi-select

                    if (response && response.length > 0) {
                        response.forEach(function(classItem) {
                            options += `<option value="${classItem.id}" data-name="${classItem.name}" data-programtitle="${classItem.program_title || ''}">
                                ${classItem.name} (${classItem.students_count || 0} students)
                            </option>`;
                        });
                    }

                    // Update dropdown and reinitialize Select2 for multi-select
                    $('#classesList').html(options);
                    $('#classesList').select2('destroy').select2({
                        theme: 'default',
                        width: '100%',
                        placeholder: 'Choose classes...',
                        allowClear: true,
                        closeOnSelect: false,
                        maximumSelectionLength: 5,
                        minimumResultsForSearch: 3,
                        templateSelection: function(selection) {
                            if (selection.id === '') return selection.text;
                            const cleanName = selection.text.split(' (')[0];
                            return cleanName;
                        },
                        language: {
                            maximumSelected: function(args) {
                                return 'You can only select ' + args.maximum + ' classes maximum.';
                            }
                        }
                    });

                    HalaqahReportSystem.ui.hideDropdownLoading('#classesList');

                    console.log(`✅ Loaded ${response.length} classes`);
                },
                error: function(xhr, status, error) {
                    console.error('❌ Error loading classes:', error);
                    HalaqahReportSystem.ui.hideDropdownLoading('#classesList');
                    HalaqahReportSystem.filters.updateFilterCardStatus('class', false);
                    $('.filter-card[data-filter="class"]').addClass('error');

                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-exclamation-triangle"></i> Failed to load classes',
                        'error',
                        5000
                    );
                }
            });
        };

        HalaqahReportSystem.loadClassMonthYears = function(classId) {
            console.log(`📅 Loading month-years for class: ${classId}`);

            HalaqahReportSystem.ui.showDropdownLoading('#monthlyHalaqahReportMonthYearList');

            const url = HalaqahReportSystem.config.routes.classMonthYears.replace('CLASS_ID', classId);
            console.log(`📡 AJAX URL: ${url}`);

            HalaqahReportSystem.requestManager.executeRequest('classMonthYears', {
                url: url,
                method: 'GET',
                data: { classId: classId },
                success: function(response) {
                    console.log(`📥 Raw response from ClassMonthYearController:`, response);

                    let options = '<option value="">📅 Select Month-Year</option>';

                    if (response && response.length > 0) {
                        console.log(`📋 Processing ${response.length} month-year items`);
                        response.forEach(function(monthYear, index) {
                            // The controller returns {month: "January", year: "2024"}
                            const monthName = monthYear.month;
                            const year = monthYear.year;
                            const value = `${monthName} ${year}`;
                            const label = `${monthName} ${year}`;

                            console.log(`  ${index + 1}. ${label} (${value})`);
                            options += `<option value="${value}" data-name="${label}">${label}</option>`;
                        });
                    } else {
                        console.log(`⚠️ No month-year data received for class ${classId}`);
                        options += '<option value="" disabled>No periods available for this class</option>';
                    }

                    console.log(`🔧 Generated options HTML:`, options);

                    HalaqahReportSystem.ui.updateDropdownOptions('#monthlyHalaqahReportMonthYearList', options);
                    HalaqahReportSystem.ui.hideDropdownLoading('#monthlyHalaqahReportMonthYearList');

                    console.log(`✅ Month-years loading completed: ${response ? response.length : 0} items`);

                    // Handle URL parameters after month-years are loaded
                    HalaqahReportSystem.ui.handleURLParametersAfterLoad('monthYear');
                },
                error: function(xhr, status, error) {
                    console.error(`❌ Error loading month-years for class ${classId}:`, error);
                    console.error('XHR Status:', xhr.status);
                    console.error('Response Text:', xhr.responseText);

                    HalaqahReportSystem.ui.hideDropdownLoading('#monthlyHalaqahReportMonthYearList');

                    // Update dropdown with error message
                    const errorOptions = '<option value="" disabled>Error loading periods</option>';
                    HalaqahReportSystem.ui.updateDropdownOptions('#monthlyHalaqahReportMonthYearList', errorOptions);

                    HalaqahReportSystem.utils.showNotification(
                        `<i class="fa fa-exclamation-triangle"></i> Failed to load periods for class ${classId}`,
                        'error',
                        5000
                    );
                }
            });
        };

        HalaqahReportSystem.loadClassStudents = function(classId) {
            console.log(`👤 Loading students for class: ${classId}`);

            // Show loading state
            HalaqahReportSystem.ui.showDropdownLoading('#studentList');
            $('#studentLoading').show();

            const url = HalaqahReportSystem.config.routes.classStudents.replace('CLASS_ID', classId);

            HalaqahReportSystem.requestManager.executeRequest('classStudents', {
                url: url,
                method: 'GET',
                data: { classId: classId },
                success: function(response) {
                    let options = '<option value="">Choose students...</option>';

                    if (response && response.length > 0) {
                        // Contextual "Select All" with class information
                        const className = $('#classesList option:selected').text() || 'this class';
                        options += `<option value="select_all" data-select-all="true" style="font-weight: bold; color: #10b981; background: #f0fdf4;">✓ Select All ${response.length} Students in ${className}</option>`;

                        response.forEach(function(student) {
                            options += `<option value="${student.id}">${student.name}</option>`;
                        });

                        // Update class context for summary
                        $('#classContext').text(className);
                    }

                    HalaqahReportSystem.ui.updateDropdownOptions('#studentList', options);
                    HalaqahReportSystem.ui.hideDropdownLoading('#studentList');

                    // Hide loading states
                    $('#studentLoading').fadeOut(300);

                    console.log(`✅ Loaded ${response.length} students`);
                },
                error: function(xhr, status, error) {
                    console.error('❌ Error loading students:', error);
                    HalaqahReportSystem.ui.hideDropdownLoading('#studentList');

                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-exclamation-triangle"></i> Failed to load students',
                        'error',
                        5000
                    );
                }
            });
        };

        // ===================================================================
        // MULTI-CLASS SELECTION FUNCTIONS
        // ===================================================================

        // Update multi-class selection UI and warnings
        HalaqahReportSystem.updateMultiClassSelection = function(selectedClasses) {
            const count = selectedClasses.length;
            const $warning = $('#multiClassWarning');
            const $summary = $('#classSelectionSummary');

            console.log(`📊 Updating multi-class selection: ${count} classes selected`);

            if (count === 0) {
                $warning.hide();
                $summary.hide();
                HalaqahReportSystem.filters.updateFilterCardStatus('class', false);
                return;
            }

            // Update filter card status
            HalaqahReportSystem.filters.updateFilterCardStatus('class', true);

            // Calculate total students across selected classes
            let totalStudents = 0;
            selectedClasses.forEach(classId => {
                const option = $(`#classesList option[value="${classId}"]`);
                const text = option.text();
                const match = text.match(/\((\d+) students\)/);
                if (match) {
                    totalStudents += parseInt(match[1]);
                }
            });

            // Update summary
            $summary.find('.selected-count').text(count);
            $summary.find('.total-students').text(`(~${totalStudents} total students)`);
            $summary.show();

            // Show warning for large exports (3+ classes or 100+ students)
            if (count >= 3 || totalStudents >= 100) {
                $warning.show();
            } else {
                $warning.hide();
            }
        };

        // Load month-years for multiple classes (intersection approach)
        HalaqahReportSystem.loadMultiClassMonthYears = function(classIds) {
            if (!classIds || classIds.length === 0) return;

            console.log(`📅 Loading month-years for classes: ${classIds.join(', ')}`);

            // Use the first class for month-year loading (can be enhanced later)
            const primaryClassId = classIds[0];
            HalaqahReportSystem.loadClassMonthYears(primaryClassId);
        };

        // Load students for multiple classes
        HalaqahReportSystem.loadMultiClassStudents = function(classIds) {
            if (!classIds || classIds.length === 0) return;

            console.log(`👥 Loading students for classes: ${classIds.join(', ')}`);

            // For now, disable student selection when multiple classes are selected
            // This will be enhanced in the student panel implementation
            $('#studentList').html('<option value="">Student selection available for single class only</option>');
            HalaqahReportSystem.filters.updateState('students', []);
        };

        // ===================================================================
        // MODERN HALAQAH REPORT SYSTEM - COMPREHENSIVE REFACTOR
        // ===================================================================

        // Continue building the HalaqahReportSystem object
       HalaqahReportSystem.utils = {
                    debounce: function(func, wait) {
                        let timeout;
                        return function executedFunction(...args) {
                            const later = () => {
                                clearTimeout(timeout);
                                func(...args);
                            };
                            clearTimeout(timeout);
                            timeout = setTimeout(later, wait);
                        };
                    },

                    showNotification: function(message, type = 'info', duration = 5000) {
                        const notification = $(`
                            <div class="alert alert-${type} alert-dismissible fade show notification-enhanced" role="alert" style="
                                position: fixed;
                                top: 20px;
                                right: 20px;
                                z-index: 9999;
                                min-width: 300px;
                                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                                border-radius: 8px;
                                border: none;
                            ">
                                <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                                ${message}
                                <button type="button" class="close" data-dismiss="alert">
                                    <span>&times;</span>
                                </button>
                            </div>
                        `);

                        $('body').append(notification);

                        if (duration > 0) {
                            setTimeout(() => {
                                notification.fadeOut(300, () => notification.remove());
                            }, duration);
                        }

                        return notification;
                    },

                    formatError: function(xhr, defaultMessage = 'An error occurred') {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            return response.message || defaultMessage;
                        } catch (e) {
                            return defaultMessage;
                        }
                    },

                    cancelRequest: function(requestKey) {
                        const request = HalaqahReportSystem.state.activeRequests.get(requestKey);
                        if (request && request.readyState !== 4) {
                            request.abort();
                            HalaqahReportSystem.state.activeRequests.delete(requestKey);
                            console.log(`Cancelled request: ${requestKey}`);
                        }
                    },

                    cancelAllRequests: function() {
                        HalaqahReportSystem.state.activeRequests.forEach((request, key) => {
                            if (request.readyState !== 4) {
                                request.abort();
                                console.log(`Cancelled request: ${key}`);
                            }
                        });
                        HalaqahReportSystem.state.activeRequests.clear();
                    }

                };


            // ===================================================================
            // DATATABLE MANAGEMENT SYSTEM
            // ===================================================================

            HalaqahReportSystem.dataTable = {
                // Common button configurations
                getCommonButtons: function() {
                    return [
                        {
                            text: '<i class="fa fa-refresh"></i>',
                            titleAttr: 'Reload Data',
                            className: 'btn btn-secondary btn-enhanced',
                            action: function(e, dt, node, config) {
                                HalaqahReportSystem.ui.showTableLoading(dt.table().node());
                                dt.ajax.reload(null, false);
                            }
                        },
                        {
                            extend: 'copyHtml5',
                            text: '<i class="fa fa-files-o"></i>',
                            titleAttr: 'Copy to Clipboard',
                            className: 'btn btn-info btn-enhanced',
                            exportOptions: {
                                columns: ':visible:not(:last-child)'
                            }
                        },
                        {
                            extend: 'excelHtml5',
                            text: '<i class="fa fa-file-excel-o"></i>',
                            titleAttr: 'Export to Excel',
                            className: 'btn btn-success btn-enhanced',
                            exportOptions: {
                                columns: ':visible:not(:last-child)'
                            }
                        },
                        {
                            extend: 'csvHtml5',
                            text: '<i class="fa fa-file-text"></i>',
                            titleAttr: 'Export to CSV',
                            className: 'btn btn-warning btn-enhanced',
                            exportOptions: {
                                columns: ':visible:not(:last-child)'
                            }
                        },
                        {
                            extend: 'pdfHtml5',
                            text: '<i class="fa fa-file-pdf-o"></i>',
                            titleAttr: 'Export to PDF',
                            className: 'btn btn-danger btn-enhanced',
                            orientation: 'landscape',
                            pageSize: 'A4',
                            exportOptions: {
                                columns: ':visible:not(:last-child)'
                            }
                        },
                        {
                            extend: 'print',
                            text: '<i class="fa fa-print"></i>',
                            titleAttr: 'Print Table',
                            className: 'btn btn-primary btn-enhanced',
                            customize: function(win) {
                                HalaqahReportSystem.dataTable.customizePrint(win, 'Halaqah Report');
                            }
                        },
                        {
                            extend: 'colvis',
                            text: '<i class="fa fa-columns"></i>',
                            titleAttr: 'Column Visibility',
                            className: 'btn btn-secondary btn-enhanced',
                            postfixButtons: ['colvisRestore']
                        }
                    ];
                },

                // Print customization function
                customizePrint: function(win, title) {
                    const teacherNames = $('#classesList').data('custom-attribute') || '';
                    const teachersList = teacherNames.split(',').join(', ');
                    const letterHead = "<?php echo e($letterHead); ?>";

                    $(win.document.body)
                        .css('font-size', '10pt')
                        .prepend(`
                            <div style="text-align: center;">
                                <img src="${letterHead}" style="width: 100%; height: auto;" />
                            </div>
                            <div style="text-align: center; font-size: 20px; font-weight: bold; margin-bottom: 10px;">
                                ${title}
                            </div>
                            <table class="print-table" style="width: 100%; margin-bottom: 20px;">
                                <tr>
                                    <td><strong>Class:</strong> ${$('#classesList option:selected').text()}</td>
                                    <td><strong>Teacher:</strong> ${teachersList}</td>
                                    <td><strong>Month:</strong> ${$('#monthlyHalaqahReportMonthYearList option:selected').text().split(' ')[0]}</td>
                                </tr>
                                <tr>
                                    <td><strong>Center:</strong> ${$('#monthlyHalaqahReportCenterList option:selected').text()}</td>
                                    <td><strong>Supervisor:</strong> ${$('#supervisorInfo').val()}</td>
                                    <td><strong>Year:</strong> ${$('#monthlyHalaqahReportMonthYearList option:selected').text().split(' ')[1]}</td>
                                </tr>
                            </table>
                        `);

                    // Remove default date/time
                    $(win.document.body).find('div.head').remove();

                    // Style tables
                    $(win.document.body).find('table')
                        .addClass('compact')
                        .css('font-size', 'inherit')
                        .attr('style', 'border: 1px solid black !important');

                    $(win.document.body).find('table td, table th')
                        .attr('style', 'border: 1px solid black !important');

                    // Add footer
                    $(win.document.body).append(`
                        <div style="position: absolute; bottom: 0; width: 100%; display: flex; justify-content: space-between; font-size: 10pt; font-weight: bold; color: darkgreen !important; margin-top: 20px;">
                            <div style="text-align: center; text-transform: uppercase; color: darkgreen !important; padding: 0 10px;">
                                YAYASAN PENDIDIKAN<br>ITQAN 529795-U
                            </div>
                            <div style="border-left: 2px solid darkgreen !important; height: 50px;"></div>
                            <div style="text-align: center; text-transform: uppercase; color: darkgreen !important; padding: 0 10px;">
                                No. ​56-2, Jalan Jernai 2, Medan Idaman,<br> Setapak 53100 Kuala Lumpur, Malaysia
                            </div>
                            <div style="border-left: 2px solid darkgreen !important; height: 50px;"></div>
                            <div style="text-align: center; color: darkgreen !important; padding: 0 10px;">
                                www.itqanalquran.org<br><EMAIL>
                            </div>
                        </div>
                    `);
                },

                // Common error handler
                handleError: function(xhr, error, thrown, tableId) {
                    console.error(`DataTable Error [${tableId}]:`, error, thrown);

                    const errorMessage = HalaqahReportSystem.utils.formatError(xhr, 'Failed to load data');

                    HalaqahReportSystem.utils.showNotification(
                        `<strong>Table Error:</strong> ${errorMessage}`,
                        'error',
                        10000
                    );

                    // Show error state in table
                    const tableWrapper = $(`#${tableId}_wrapper`);
                    if (tableWrapper.length) {
                        tableWrapper.find('.dataTables_empty').html(`
                            <div class="error-state">
                                <i class="fa fa-exclamation-triangle text-danger"></i>
                                <h5>Error Loading Data</h5>
                                <p>${errorMessage}</p>
                                <button class="btn btn-primary btn-sm" onclick="HalaqahReportSystem.dataTable.retryTable('${tableId}')">
                                    <i class="fa fa-refresh"></i> Retry
                                </button>
                            </div>
                        `);
                    }
                },

                // Retry table loading
                retryTable: function(tableId) {
                    const instance = HalaqahReportSystem.state.dataTableInstances.get(tableId);
                    if (instance) {
                        HalaqahReportSystem.ui.showTableLoading(`#${tableId}`);
                        instance.ajax.reload(null, false);
                    }
                }
            };

            // ===================================================================
            // UI MANAGEMENT SYSTEM
            // ===================================================================

            HalaqahReportSystem.ui = {
                // Loading states
                showDropdownLoading: function(selector) {
                    const container = $(selector).closest('.dropdown-container');
                    container.addClass('loading');
                    $(selector).prop('disabled', true);

                    // Add skeleton loading if empty
                    if ($(selector).find('option').length <= 1) {
                        $(selector).html('<option value="">Loading...</option>');
                    }
                },

                hideDropdownLoading: function(selector) {
                    // Use filter-card as the standard container for modern UI
                    const card = $(selector).closest('.filter-card');
                    card.removeClass('loading');
                    $(selector).prop('disabled', false);
                },

                showTableLoading: function(tableSelector) {
                    const wrapper = $(tableSelector).closest('.dataTables_wrapper');
                    wrapper.addClass('filter-loading');

                    // Add skeleton rows if table is empty
                    const tbody = $(tableSelector).find('tbody');
                    if (tbody.find('tr').length === 0) {
                        const skeletonRows = Array.from({length: 5}, () => `
                            <tr class="skeleton-row">
                                <td><div class="skeleton-loading"></div></td>
                                <td><div class="skeleton-loading"></div></td>
                                <td><div class="skeleton-loading"></div></td>
                                <td><div class="skeleton-loading"></div></td>
                                <td><div class="skeleton-loading"></div></td>
                                <td><div class="skeleton-loading"></div></td>
                                <td><div class="skeleton-loading"></div></td>
                            </tr>
                        `).join('');
                        tbody.html(skeletonRows);
                    }
                },

                hideTableLoading: function(tableSelector) {
                    const wrapper = $(tableSelector).closest('.dataTables_wrapper');
                    wrapper.removeClass('filter-loading');

                    // Remove skeleton rows
                    $(tableSelector).find('.skeleton-row').remove();
                },

                // Enhanced dropdown updates with animations (Select2 compatible)
                updateDropdownOptions: function(selector, options, selectedValue = '') {
                    const $dropdown = $(selector);
                    const container = $dropdown.closest('.dropdown-container, .filter-card');

                    console.log(`🔄 Updating dropdown ${selector} with ${options.split('<option').length - 1} options`);

                    // Check if dropdown element exists
                    if ($dropdown.length === 0) {
                        console.error(`❌ Dropdown element not found: ${selector}`);
                        return;
                    }

                    console.log(`📍 Dropdown element found: ${selector}, length: ${$dropdown.length}`);

                    // Check if Select2 is initialized
                    const isSelect2 = $dropdown.hasClass('select2-hidden-accessible');
                    console.log(`🔍 Is Select2 initialized: ${isSelect2}`);

                    if (isSelect2) {
                        // For Select2 dropdowns
                        console.log(`📋 Updating Select2 dropdown: ${selector}`);
                        console.log(`🔧 Current dropdown HTML before update:`, $dropdown.html());
                        console.log(`🔧 New options to set:`, options);

                        // Update the original select element
                        $dropdown.html(options);

                        // Set selected value if provided
                        if (selectedValue) {
                            $dropdown.val(selectedValue);
                            console.log(`🎯 Set selected value: ${selectedValue}`);
                        }

                        // Trigger Select2 to refresh
                        $dropdown.trigger('change');

                        console.log(`🔧 Dropdown HTML after update:`, $dropdown.html());
                        console.log(`✅ Select2 dropdown ${selector} updated successfully`);
                    } else {
                        // For regular dropdowns
                        console.log(`📋 Updating regular dropdown: ${selector}`);

                        $dropdown.fadeOut(150, function() {
                            $dropdown.html(options);

                            if (selectedValue) {
                                $dropdown.val(selectedValue);
                            }

                            // Fade in
                            $dropdown.fadeIn(150);
                        });
                    }

                    // Add visual feedback
                    if (container.length) {
                        container.addClass('focused');
                        setTimeout(() => container.removeClass('focused'), 1000);
                    }
                },

                // Student count badge update
                updateStudentCount: function(count) {
                    const badge = $('#selectedStudentCount');
                    if (count > 0) {
                        badge.text(count).fadeIn(200);
                        badge.removeClass('badge-secondary').addClass('badge-success');
                    } else {
                        badge.fadeOut(200);
                    }
                },



                // Enhanced student count display
                updateStudentCount: function(count) {
                    HalaqahReportSystem.filters.updateStudentDisplay(
                        HalaqahReportSystem.state.filterValues.students
                    );
                },

                // Show/hide student action buttons (updated for new design)
                toggleStudentButtons: function(show) {
                    // This function is no longer needed with the new design
                    // Student actions are now integrated into the dropdown
                },

                // Enhanced validation messages
                showValidationMessage: function(message, type = 'warning') {
                    const alertHtml = `
                        <div class="alert alert-${type} alert-dismissible fade show validation-message" style="
                            border-radius: 8px;
                            border: none;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                            animation: slideInDown 0.3s ease;
                        ">
                            <i class="fa fa-${type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                            ${message}
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    `;

                    $('.validation-message').remove();
                    $('.filter-container').after(alertHtml);

                    // Auto-hide after 5 seconds
                    setTimeout(() => {
                        $('.validation-message').fadeOut(300, function() {
                            $(this).remove();
                        });
                    }, 5000);
                },

                hideValidationMessage: function() {
                    $('.validation-message').fadeOut(300, function() {
                        $(this).remove();
                    });
                },

                // Show export progress modal for large exports
                showExportProgressModal: function() {
                    const modalHtml = `
                        <div class="modal fade" id="exportProgressModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false">
                            <div class="modal-dialog modal-dialog-centered" role="document">
                                <div class="modal-content" style="border-radius: 12px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.2);">
                                    <div class="modal-header" style="border-bottom: 1px solid #e9ecef; padding: 20px 24px;">
                                        <h5 class="modal-title" style="font-weight: 600; color: #2d3748;">
                                            <i class="fa fa-download text-primary"></i> Exporting Reports
                                        </h5>
                                    </div>
                                    <div class="modal-body" style="padding: 24px;">
                                        <div class="text-center">
                                            <div class="export-progress-container" style="margin-bottom: 20px;">
                                                <div class="progress" style="height: 8px; border-radius: 4px; background: #e2e8f0;">
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                         style="background: linear-gradient(90deg, #3b82f6, #10b981); width: 0%;"
                                                         id="exportProgressBar"></div>
                                                </div>
                                                <div class="progress-text" style="margin-top: 8px; font-size: 14px; color: #64748b;">
                                                    <span id="exportProgressText">Preparing export...</span>
                                                </div>
                                            </div>
                                            <div class="export-status" style="color: #64748b; font-size: 13px;">
                                                <i class="fa fa-info-circle"></i>
                                                Large exports may take several minutes to complete.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove existing modal if present
                    $('#exportProgressModal').remove();

                    // Add modal to body
                    $('body').append(modalHtml);

                    // Show modal
                    $('#exportProgressModal').modal('show');

                    // Start progress simulation
                    this.simulateExportProgress();
                },

                // Simulate export progress for user feedback
                simulateExportProgress: function() {
                    let progress = 0;
                    const progressSteps = [
                        { progress: 15, text: 'Loading class data...' },
                        { progress: 30, text: 'Processing student records...' },
                        { progress: 50, text: 'Generating memorization reports...' },
                        { progress: 70, text: 'Generating revision reports...' },
                        { progress: 85, text: 'Formatting Excel sheets...' },
                        { progress: 95, text: 'Finalizing export...' }
                    ];

                    let currentStep = 0;
                    const updateProgress = () => {
                        if (currentStep < progressSteps.length) {
                            const step = progressSteps[currentStep];
                            $('#exportProgressBar').css('width', step.progress + '%');
                            $('#exportProgressText').text(step.text);
                            currentStep++;

                            // Randomize timing to feel more natural
                            const delay = Math.random() * 2000 + 1000; // 1-3 seconds
                            setTimeout(updateProgress, delay);
                        }
                    };

                    updateProgress();
                },

                // Hide export progress modal
                hideExportProgressModal: function() {
                    $('#exportProgressModal').modal('hide');
                    setTimeout(() => {
                        $('#exportProgressModal').remove();
                    }, 300);
                },

                // Handle URL parameters after data is loaded
                handleURLParametersAfterLoad: function(loadType) {
                    try {
                        console.log(`🔗 Handling URL parameters after ${loadType} loaded`);

                        // Get URL parameters
                        const urlParams = new URLSearchParams(window.location.search);
                        const centerId = urlParams.get('centerId');
                        const classId = urlParams.get('classId');
                        const monthYear = urlParams.get('monthYear');

                        console.log(`📋 URL Parameters: center=${centerId}, class=${classId}, monthYear=${monthYear}`);

                        // Handle different load types
                        switch(loadType) {
                            case 'classes':
                                // After classes are loaded, set the class if specified in URL
                                if (classId) {
                                    const classOption = $(`#classesList option[value="${classId}"]`);
                                    if (classOption.length > 0) {
                                        console.log(`🎯 Setting class from URL: ${classId}`);
                                        $('#classesList').val(classId).trigger('change');
                                    } else {
                                        console.log(`⚠️ Class ${classId} not found in dropdown options`);
                                    }
                                }
                                break;

                            case 'monthYear':
                                // After month-years are loaded, set the month-year if specified in URL
                                if (monthYear) {
                                    const monthYearOption = $(`#monthlyHalaqahReportMonthYearList option[value="${monthYear}"]`);
                                    if (monthYearOption.length > 0) {
                                        console.log(`🎯 Setting month-year from URL: ${monthYear}`);
                                        $('#monthlyHalaqahReportMonthYearList').val(monthYear).trigger('change');
                                    } else {
                                        console.log(`⚠️ Month-year ${monthYear} not found in dropdown options`);
                                    }
                                }
                                break;

                            case 'students':
                                // After students are loaded, we don't auto-select students from URL
                                // This is intentional as student selection should be manual
                                console.log('📚 Students loaded, no auto-selection from URL');
                                break;

                            default:
                                console.log(`⚠️ Unknown load type: ${loadType}`);
                        }
                    } catch (error) {
                        console.error(`❌ Error in handleURLParametersAfterLoad(${loadType}):`, error);
                    }
                }
            };

            // ===================================================================
            // MODERN FILTER MANAGEMENT SYSTEM
            // ===================================================================

            HalaqahReportSystem.filters = {
                // Validate required filters
                validateRequired: function() {
                    const state = HalaqahReportSystem.state.filterValues;
                    const hasClass = Array.isArray(state.class) ? state.class.length > 0 : !!state.class;
                    return state.center && hasClass && state.monthYear;
                },

                // Update filter state with modern UI feedback
                updateState: function(filterName, value) {
                    const state = HalaqahReportSystem.state.filterValues;

                    if (filterName === 'students') {
                        state.students = Array.isArray(value) ? value : [value].filter(Boolean);
                        this.updateStudentDisplay(state.students);
                    } else if (filterName === 'class') {
                        // Handle both single class and multi-class selection
                        state[filterName] = Array.isArray(value) ? value : (value ? [value] : []);
                        const hasValue = state[filterName].length > 0;
                        this.updateFilterCardStatus(filterName, hasValue);
                    } else {
                        state[filterName] = value;
                        this.updateFilterCardStatus(filterName, !!value);
                    }

                    console.log('Filter state updated:', state);
                    this.checkFiltersAndGenerate();
                    this.updateUI();
                },

                // Update filter card visual status
                updateFilterCardStatus: function(filterName, isActive) {
                    const card = $(`.filter-card[data-filter="${filterName}"]`);
                    const statusIcon = card.find('.filter-status .fa');

                    if (isActive) {
                        card.addClass('user-active').removeClass('error');
                        statusIcon.removeClass('fa-circle-o fa-exclamation-triangle').addClass('fa-check-circle');
                    } else {
                        card.removeClass('user-active error');
                        statusIcon.removeClass('fa-check-circle fa-exclamation-triangle').addClass('fa-circle-o');
                    }
                },

                // Update progress indicator
                // ULTRATHINKING: Intelligent debounced report generation
                checkFiltersAndGenerate: function() {
                    const state = HalaqahReportSystem.state.filterValues;
                    const requiredFilters = ['center', 'class', 'monthYear'];
                    const isValid = requiredFilters.every(filter => state[filter]);

                    if (isValid) {
                        this.showActionButtons();
                        // Intelligent debouncing to prevent double AJAX calls
                        this.debouncedGenerate();
                    } else {
                        this.hideActionButtons();
                    }
                },

                // ULTRATHINKING: Debounced generation with single-point-of-truth
                debouncedGenerate: function() {
                    const now = Date.now();
                    const timeSinceLastGeneration = now - HalaqahReportSystem.state.lastGenerationTime;

                    // Prevent rapid successive calls
                    if (timeSinceLastGeneration < HalaqahReportSystem.state.generationDebounceMs) {
                        console.log('🚫 Generation debounced - too soon since last call');
                        return;
                    }

                    // Prevent concurrent generations
                    if (HalaqahReportSystem.state.isGenerating) {
                        console.log('🚫 Generation already in progress');
                        return;
                    }

                    HalaqahReportSystem.state.lastGenerationTime = now;
                    HalaqahReportSystem.state.isGenerating = true;

                    console.log('🚀 Initiating intelligent report generation...');

                    // Add visual feedback
                    this.showGenerationFeedback();

                    setTimeout(() => {
                        HalaqahReportSystem.dataTable.initializeAll();
                        HalaqahReportSystem.state.isGenerating = false;
                        this.hideGenerationFeedback();
                    }, 300);
                },

                // ULTRATHINKING: Visual feedback for generation process
                showGenerationFeedback: function() {
                    const actionBar = $('.filter-action-bar');
                    if (!actionBar.find('.generation-indicator').length) {
                        actionBar.append(`
                            <div class="generation-indicator">
                                <div class="generation-spinner"></div>
                                <span>Generating reports...</span>
                            </div>
                        `);
                    }
                },

                hideGenerationFeedback: function() {
                    $('.generation-indicator').fadeOut(300, function() {
                        $(this).remove();
                    });
                },

                // CRITICAL FIX: Update student display with proper container management
                updateStudentDisplay: function(students) {
                    const count = students.length;
                    const countText = $('#selectedStudentCount');
                    const selectionDisplay = $('#studentSelectionDisplay');
                    const selectedStudentsList = $('#selectedStudentsList');
                    const clearIcon = $('#studentClearIcon');
                    const searchContainer = $('#studentSearchContainer');
                    const filterCard = $('.student-filter-card');
                    const className = $('#classesList option:selected').text() || 'this class';

                    if (count > 0) {
                        // Update count and context (adjacent placement)
                        countText.text(count);
                        $('#classContext').text(className);

                        // Show selection display with proper height management
                        selectionDisplay.slideDown(300);
                        clearIcon.show();

                        // Show search if more than 5 students
                        if (count > 5) {
                            searchContainer.slideDown(300);
                        } else {
                            searchContainer.slideUp(300);
                        }

                        // Populate selected students list
                        this.populateSelectedStudentsList(students);

                        // Match visual hierarchy of other filters
                        filterCard.addClass('active');
                        this.updateFilterCardStatus('students', true);

                        console.log(`👥 ${count} students selected from ${className}`);
                    } else {
                        // Hide all selection displays
                        selectionDisplay.slideUp(300);
                        searchContainer.slideUp(300);
                        clearIcon.hide();

                        // Clear the list
                        selectedStudentsList.empty();

                        // Reset visual state
                        filterCard.removeClass('active');
                        this.updateFilterCardStatus('students', false);

                        console.log('👥 Student selection cleared');
                    }
                },

                // CRITICAL FIX: Populate selected students list with individual remove buttons
                populateSelectedStudentsList: function(studentIds) {
                    const selectedStudentsList = $('#selectedStudentsList');
                    selectedStudentsList.empty();

                    if (studentIds.length === 0) {
                        selectedStudentsList.html('<div style="padding: 12px; text-align: center; color: #64748b; font-size: 13px;">No students selected</div>');
                        return;
                    }

                    studentIds.forEach(studentId => {
                        const studentOption = $(`#studentList option[value="${studentId}"]`);
                        const studentName = studentOption.text();

                        if (studentName && studentId !== 'select_all') {
                            const studentItem = $(`
                                <div class="selected-student-item" data-student-id="${studentId}">
                                    <span class="student-name">${studentName}</span>
                                    <button type="button" class="remove-student-btn" data-student-id="${studentId}">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            `);

                            selectedStudentsList.append(studentItem);
                        }
                    });

                    // Bind remove button events
                    $('.remove-student-btn').off('click.removeStudent').on('click.removeStudent', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const studentId = $(this).data('student-id');
                        const currentValues = $('#studentList').val() || [];
                        const newValues = currentValues.filter(id => id !== studentId.toString());

                        $('#studentList').val(newValues).trigger('change');

                        console.log(`🗑️ Removed student: ${studentId}`);
                    });
                },

                // Update student summary display
                updateStudentSummary: function(students) {
                    const summaryCount = $('.summary-count');
                    const studentsList = $('#selectedStudentsList');

                    summaryCount.text(`${students.length} student${students.length !== 1 ? 's' : ''} selected`);

                    // Create student tags
                    const studentTags = students.map(studentId => {
                        const option = $(`#studentList option[value="${studentId}"]`);
                        const studentName = option.text();
                        return `
                            <div class="student-tag">
                                <span>${studentName}</span>
                                <button type="button" class="remove-student" data-student-id="${studentId}">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        `;
                    }).join('');

                    studentsList.html(studentTags);
                },

                // Update UI based on current state
                updateUI: function() {
                    const isValid = this.validateRequired();
                    const state = HalaqahReportSystem.state.filterValues;

                    // Update action buttons
                    const hasAnyFilter = state.center || state.class || state.monthYear || state.students.length > 0;
                    $('#clearFiltersBtn').prop('disabled', !hasAnyFilter);
                    $('#applyFiltersBtn').prop('disabled', !isValid);

                    // Student actions are now integrated into the dropdown
                    // No separate action buttons needed

                    // ULTRATHINKING: Removed duplicate generation call - now handled by checkFiltersAndGenerate()
                    if (isValid) {
                        this.showActionButtons();
                        this.showDataTables();
                    } else {
                        this.hideActionButtons();
                        this.hideDataTables();
                    }
                },

                // Show action buttons
                showActionButtons: function() {
                    const state = HalaqahReportSystem.state.filterValues;
                    const baseUrl = $('#baseUrl').val();

                    if (baseUrl) {
                        const printUrl = baseUrl
                            .replace('CLASS_ID', state.class)
                            .replace('CENTER_ID', state.center)
                            .replace('MONTH_YEAR', state.monthYear);

                        $('#printButton').attr('href', printUrl).fadeIn(300);
                    }

                    $('#exportExcelButton').fadeIn(300);
                },

                // Hide action buttons
                hideActionButtons: function() {
                    $('#printButton, #exportExcelButton').fadeOut(300);
                },

                // Show/hide data tables
                showDataTables: function() {
                    $('#otherTables').slideDown(400);
                },

                hideDataTables: function() {
                    $('#otherTables').slideUp(400);
                },

                // Clear all filters with enhanced feedback
                clearAll: function() {
                    console.log('Clearing all filters...');

                    // Cancel active requests
                    HalaqahReportSystem.utils.cancelAllRequests();

                    // Reset state
                    const state = HalaqahReportSystem.state.filterValues;
                    state.center = '';
                    state.class = '';
                    state.monthYear = '';
                    state.students = [];

                    // Reset dropdowns
                    $('#monthlyHalaqahReportCenterList').val('').trigger('change');
                    $('#classesList').val('').trigger('change');
                    $('#monthlyHalaqahReportMonthYearList').val('').trigger('change');
                    $('#studentList').val(null).trigger('change');

                    // Reset all filter cards
                    $('.filter-card').removeClass('user-active error');
                    $('.filter-status .fa').removeClass('fa-check-circle fa-exclamation-triangle').addClass('fa-circle-o');

                    // Reset filter states

                    // CRITICAL FIX: Reset all student displays
                    $('#studentSelectionDisplay').hide();
                    $('#studentSearchContainer').hide();
                    $('#studentClearIcon').hide();
                    $('#studentLoading').hide();
                    $('#selectedStudentsList').empty();
                    $('.student-filter-card').removeClass('active');

                    // Update UI
                    this.updateUI();
                    HalaqahReportSystem.ui.hideValidationMessage();

                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-check"></i> All filters cleared successfully',
                        'success',
                        3000
                    );
                }
            };

            // ===================================================================
            // DATATABLE INITIALIZATION SYSTEM
            // ===================================================================

            HalaqahReportSystem.dataTable.initializeAll = function() {
                if (HalaqahReportSystem.state.isInitialized) {
                    console.log('DataTables already initialized, refreshing...');
                    this.refreshAll();
                    return;
                }

                console.log('Initializing all DataTables...');
                HalaqahReportSystem.state.isInitialized = true;

                // Show tables container with animation
                HalaqahReportSystem.filters.showDataTables();

                // Initialize each table
                this.initializeMemorizationTable();
                this.initializeRevisionTable();
                this.initializeSummaryTable();
            };

            HalaqahReportSystem.dataTable.refreshAll = function() {
                console.log('Refreshing all DataTables...');

                HalaqahReportSystem.state.dataTableInstances.forEach((instance, tableId) => {
                    if (instance && typeof instance.ajax !== 'undefined') {
                        console.log(`Refreshing DataTable: ${tableId}`);
                        HalaqahReportSystem.ui.showTableLoading(`#${tableId}`);
                        instance.ajax.reload(null, false);
                    }
                });
            };

            // Memorization Table Configuration
            HalaqahReportSystem.dataTable.initializeMemorizationTable = function() {
                const tableId = 'MonthlyHalaqahReportDatatable';

                if (HalaqahReportSystem.state.dataTableInstances.has(tableId)) {
                    return; // Already initialized
                }

                console.log(`Initializing ${tableId}...`);

                const config = {
                    searching: false,
                    autoWidth: false,
                    paging: true,
                    info: false,
                    ordering: false,
                    serverSide: true,
                    processing: true,
                    destroy: true,
                    responsive: true,
                    dom: 'Bfrtip',
                    buttons: this.getCommonButtons(),
                    ajax: {
                        url: HalaqahReportSystem.config.routes.halaqahReport,
                        data: function(d) {
                            const state = HalaqahReportSystem.state.filterValues;
                            d.classId = state.class;
                            d.classDate = state.monthYear;
                            d.studentId = state.students.length > 0 ? state.students : null;
                        },
                        error: function(xhr, error, thrown) {
                            HalaqahReportSystem.dataTable.handleError(xhr, error, thrown, tableId);
                        }
                    },
                    columns: [
                        {data: 'DT_RowIndex', name: 'DT_RowIndex', title: '#', width: '5%', className: 'text-center', orderable: false, searchable: false},
                        {data: 'student', name: 'student', title: 'Student', width: '18%', className: 'text-left'},
                        {data: 'monthlyHefzPlan', name: 'monthlyHefzPlan', title: 'Monthly Plan', width: '20%', className: 'text-left'},
                        {data: 'memorizedPages', name: 'memorizedPages', title: 'Memorized Pages', width: '12%', className: 'text-center'},
                        {data: 'monthlyHefzReport', name: 'monthlyHefzReport', title: 'Monthly Report', width: '20%', className: 'text-left'},
                        {data: 'attendanceDaysPercentage', name: 'attendanceDaysPercentage', title: 'Attendance %', width: '12%', className: 'text-center', render: function(data, type, row) {
                            if (type === 'display' && data) {
                                return '<div style="white-space: nowrap; min-width: 80px;">' + data + '</div>';
                            }
                            return data || '0%';
                        }},
                        {data: 'hefzAchievementComparedtoHefzPlan', name: 'hefzAchievementComparedtoHefzPlan', title: 'Achievement %', width: '13%', className: 'text-center', render: function(data, type, row) {
                            if (type === 'display' && data) {
                                return '<div style="white-space: nowrap; min-width: 80px;">' + data + '</div>';
                            }
                            return data || '0%';
                        }}
                    ],
                    drawCallback: function(settings) {
                        HalaqahReportSystem.ui.hideTableLoading(`#${tableId}`);
                        console.log(`${tableId} draw complete`);
                    },
                    initComplete: function(settings, json) {
                        const recordCount = json?.recordsTotal || 0;
                        const filteredCount = json?.recordsFiltered || 0;

                        console.log(`✅ ${tableId} initialization complete - ${recordCount} total records, ${filteredCount} displayed`);

                        // Show success notification
                        if (recordCount > 0) {
                            HalaqahReportSystem.utils.showNotification(
                                `<i class="fa fa-check"></i> Memorization table loaded successfully with ${recordCount} students`,
                                'success',
                                3000
                            );
                        } else {
                            HalaqahReportSystem.utils.showNotification(
                                '<i class="fa fa-info-circle"></i> No memorization data found for the selected filters',
                                'info',
                                4000
                            );
                        }
                    }
                };

                const instance = $(`#${tableId}`).DataTable(config);
                HalaqahReportSystem.state.dataTableInstances.set(tableId, instance);
            };

            // Revision Table Configuration
            HalaqahReportSystem.dataTable.initializeRevisionTable = function() {
                const tableId = 'monthlyStudentRevisionReportTable';

                if (HalaqahReportSystem.state.dataTableInstances.has(tableId)) {
                    return; // Already initialized
                }

                console.log(`Initializing ${tableId}...`);

                const config = {
                    searching: false,
                    autoWidth: false,
                    paging: true,
                    info: false,
                    ordering: false,
                    serverSide: true,
                    processing: true,
                    destroy: true,
                    responsive: true,
                    dom: 'Bfrtip',
                    buttons: this.getCommonButtons(),
                    ajax: {
                        url: HalaqahReportSystem.config.routes.revisionReport,
                        data: function(d) {
                            const state = HalaqahReportSystem.state.filterValues;
                            d.classId = state.class;
                            d.classDate = state.monthYear;
                            d.studentId = state.students.length > 0 ? state.students : null;
                        },
                        error: function(xhr, error, thrown) {
                            HalaqahReportSystem.dataTable.handleError(xhr, error, thrown, tableId);
                        }
                    },
                    columns: [
                        {data: 'DT_RowIndex', name: 'DT_RowIndex', title: '#', width: '5%', className: 'text-center', orderable: false, searchable: false},
                        {data: 'student', name: 'student', title: 'Student', width: '20%', className: 'text-left'},
                        {data: 'monthlyRevisionPlan', name: 'monthlyRevisionPlan', title: 'Revision Plan', width: '20%', className: 'text-left'},
                        {data: 'monthlyRevisionReport', name: 'monthlyRevisionReport', title: 'Revision Report', width: '20%', className: 'text-left'},
                        {data: 'revisedPages', name: 'revisedPages', title: 'Revised Pages', width: '15%', className: 'text-center'},
                        {data: 'attendanceDaysPercentage', name: 'attendanceDaysPercentage', title: 'Attendance %', width: '10%', className: 'text-center', render: function(data, type, row) {
                            if (type === 'display' && data) {
                                return '<div style="white-space: nowrap; min-width: 80px;">' + data + '</div>';
                            }
                            return data || '0%';
                        }},
                        {data: 'revisionAchievementComparedtoHefzPlan', name: 'revisionAchievementComparedtoHefzPlan', title: 'Achievement %', width: '10%', className: 'text-center', render: function(data, type, row) {
                            if (type === 'display' && data) {
                                return '<div style="white-space: nowrap; min-width: 80px;">' + data + '</div>';
                            }
                            return data || '0%';
                        }}
                    ],
                    drawCallback: function(settings) {
                        HalaqahReportSystem.ui.hideTableLoading(`#${tableId}`);

                        // Add hover effects to rows
                        $(`#${tableId} tbody tr`).hover(
                            function() { $(this).addClass('table-hover-effect'); },
                            function() { $(this).removeClass('table-hover-effect'); }
                        );
                    },
                    initComplete: function(settings, json) {
                        console.log(`${tableId} initialization complete`);
                        HalaqahReportSystem.utils.showNotification(
                            '<i class="fa fa-check"></i> Revision table loaded successfully',
                            'success',
                            3000
                        );
                    }
                };

                const instance = $(`#${tableId}`).DataTable(config);
                HalaqahReportSystem.state.dataTableInstances.set(tableId, instance);
            };

            // Summary Table Configuration
            HalaqahReportSystem.dataTable.initializeSummaryTable = function() {
                const tableId = 'halaqahSummaryTable';

                if (HalaqahReportSystem.state.dataTableInstances.has(tableId)) {
                    return; // Already initialized
                }

                console.log(`Initializing ${tableId}...`);

                const config = {
                    searching: false,
                    autoWidth: true,
                    paging: false,
                    info: false,
                    ordering: false,
                    serverSide: true,
                    processing: true,
                    destroy: true,
                    responsive: true,
                    dom: 'Bfrtip',
                    buttons: this.getCommonButtons(),
                    ajax: {
                        url: HalaqahReportSystem.config.routes.summaryReport,
                        data: function(d) {
                            const state = HalaqahReportSystem.state.filterValues;
                            d.classId = state.class;
                            d.classDate = state.monthYear;
                            d.studentId = state.students.length > 0 ? state.students : null;
                        },
                        error: function(xhr, error, thrown) {
                            HalaqahReportSystem.dataTable.handleError(xhr, error, thrown, tableId);
                        }
                    },
                    columns: [
                        {data: 'noOfStudents', name: 'noOfStudents', title: 'No of Students', width: '20%', className: 'text-center'},
                        {data: 'memorizedPages', name: 'memorizedPages', title: 'Total Memorized Pages', width: '20%', className: 'text-center'},
                        {data: 'revisedPages', name: 'revisedPages', title: 'Total Revised Pages', width: '20%', className: 'text-center'},
                        {data: 'attendanceDaysPercentage', name: 'attendanceDaysPercentage', title: 'Attendance %', width: '20%', className: 'text-center', render: function(data, type, row) {
                            if (type === 'display' && data) {
                                return '<div style="white-space: nowrap; min-width: 80px;">' + data + '</div>';
                            }
                            return data || '0%';
                        }},
                        {data: 'hefzAchievementComparedtoHefzPlan', name: 'hefzAchievementComparedtoHefzPlan', title: 'Achievement %', width: '20%', className: 'text-center', render: function(data, type, row) {
                            if (type === 'display' && data) {
                                return '<div style="white-space: nowrap; min-width: 80px;">' + data + '</div>';
                            }
                            return data || '0%';
                        }}
                    ],
                    drawCallback: function(settings) {
                        HalaqahReportSystem.ui.hideTableLoading(`#${tableId}`);

                        // Add hover effects to rows
                        $(`#${tableId} tbody tr`).hover(
                            function() { $(this).addClass('table-hover-effect'); },
                            function() { $(this).removeClass('table-hover-effect'); }
                        );
                    },
                    initComplete: function(settings, json) {
                        console.log(`${tableId} initialization complete`);
                        HalaqahReportSystem.utils.showNotification(
                            '<i class="fa fa-check"></i> Summary table loaded successfully',
                            'success',
                            3000
                        );
                    }
                };

                const instance = $(`#${tableId}`).DataTable(config);
                HalaqahReportSystem.state.dataTableInstances.set(tableId, instance);
            };






            // ===================================================================
            // INITIAL SETUP
            // ===================================================================

            // ===================================================================
            // LEGACY EVENT HANDLERS - CONVERTED TO PROPER METHOD ASSIGNMENTS
            // ===================================================================

            // Center change handler
            HalaqahReportSystem.handleCenterChange = function() {
                const centerId = $('#monthlyHalaqahReportCenterList').val();
                const centerName = $('#monthlyHalaqahReportCenterList option:selected').data('name');

                console.log('Center changed:', centerId, centerName);

                // Update state
                HalaqahReportSystem.filters.updateState('center', centerId);

                // Reset dependent filters
                HalaqahReportSystem.filters.updateState('class', '');
                HalaqahReportSystem.filters.updateState('monthYear', '');
                HalaqahReportSystem.filters.updateState('students', []);

                // Update UI elements
                $('.summaryCenterName').text(centerName || '');

                if (centerId) {
                    HalaqahReportSystem.loadCenterClasses(centerId);
                    HalaqahReportSystem.loadCenterSupervisor(centerId);
                } else {
                    // Clear dependent dropdowns
                    HalaqahReportSystem.ui.updateDropdownOptions('#classesList', '<option value="">👥 Select Class</option>');
                    HalaqahReportSystem.ui.updateDropdownOptions('#monthlyHalaqahReportMonthYearList', '<option value="">📅 Select Month-Year</option>');
                    HalaqahReportSystem.ui.updateDropdownOptions('#studentList', '<option value="">👤 Select Students (Multiple)</option>');
                }
            };

            // Load classes for selected center
            HalaqahReportSystem.loadCenterClasses = function(centerId) {
                    console.log('Loading classes for center:', centerId);

                    // Cancel previous request
                    HalaqahReportSystem.utils.cancelRequest('centerClasses');

                    // Show loading
                    HalaqahReportSystem.ui.showDropdownLoading('#classesList');

                    const request = $.ajax({
                        type: 'GET',
                        url: `${HalaqahReportSystem.config.routes.centerClasses}/${centerId}`,
                        success: (data) => {
                            HalaqahReportSystem.state.activeRequests.delete('centerClasses');
                            HalaqahReportSystem.ui.hideDropdownLoading('#classesList');

                            let options = '<option value="">👥 Select Class</option>';
                            data.forEach(cls => {
                                options += `<option data-name="${cls.name}" value="${cls.id}">${cls.name}</option>`;
                            });

                            HalaqahReportSystem.ui.updateDropdownOptions('#classesList', options);

                            HalaqahReportSystem.utils.showNotification(
                                `<i class="fa fa-building"></i> Loaded ${data.length} classes`,
                                'success',
                                3000
                            );

                            // Handle URL parameters after classes are loaded
                            HalaqahReportSystem.ui.handleURLParametersAfterLoad('classes');
                        },
                        error: (xhr, status, error) => {
                            HalaqahReportSystem.state.activeRequests.delete('centerClasses');
                            if (xhr.statusText !== 'abort') {
                                HalaqahReportSystem.ui.hideDropdownLoading('#classesList');

                                const errorMsg = HalaqahReportSystem.utils.formatError(xhr, 'Failed to load classes');
                                HalaqahReportSystem.ui.updateDropdownOptions('#classesList', `<option value="">Error: ${errorMsg}</option>`);

                                HalaqahReportSystem.utils.showNotification(
                                    `<strong>Error:</strong> ${errorMsg}`,
                                    'error',
                                    8000
                                );
                            }
                        }
                    });

                    HalaqahReportSystem.state.activeRequests.set('centerClasses', request);
                };

            // Load supervisor for selected center
            HalaqahReportSystem.loadCenterSupervisor = function(centerId) {
                $.ajax({
                    type: 'GET',
                    url: `${HalaqahReportSystem.config.routes.centerSupervisor}/${centerId}`,
                    success: (data) => {
                        $('#supervisorInfo').val(data.supervisor || '');
                    },
                    error: (xhr, status, error) => {
                        console.warn('Failed to load supervisor info:', error);
                    }
                });
            };

            // Class change handler
            HalaqahReportSystem.handleClassChange = function() {
                const classId = $('#classesList').val();
                const className = $('#classesList option:selected').data('name');
                const programTitle = $('#classesList option:selected').data('programtitle');

                console.log('Class changed:', classId, className, programTitle);

                // Update state
                HalaqahReportSystem.filters.updateState('class', classId);

                // Reset dependent filters (but don't clear them from UI yet)
                HalaqahReportSystem.filters.updateState('monthYear', '');
                HalaqahReportSystem.filters.updateState('students', []);

                // Update UI elements
                $('.summaryClassName').text(className || '');

                if (classId) {
                    // Update class details URL
                    const classUrl = "<?php echo e(route('classes.show', ['id' => 0])); ?>".replace('/0', `/${classId}`);
                    $('.classDetailsUrl').attr('href', classUrl);

                    // Visual feedback for class selection
                    $('.classDetailsUrl').css({
                        'background-color': '#28a745',
                        'color': 'white',
                        'transition': 'all 0.3s ease'
                    });
                    setTimeout(() => {
                        $('.classDetailsUrl').css({
                            'background-color': '',
                            'color': ''
                        });
                    }, 2000);

                    // Load dependent data
                    HalaqahReportSystem.loadClassMonthYears(classId);
                    HalaqahReportSystem.loadClassStudents(classId);
                    HalaqahReportSystem.loadClassTeachers(classId);
                } else {
                    // Clear dependent dropdowns
                    HalaqahReportSystem.ui.updateDropdownOptions('#monthlyHalaqahReportMonthYearList', '<option value="">📅 Select Month-Year</option>');
                    HalaqahReportSystem.ui.updateDropdownOptions('#studentList', '<option value="">👤 Select Students (Multiple)</option>');
                }
            };




            // Load students for selected class
            HalaqahReportSystem.loadClassStudents = function(classId, monthYear = null) {
                    console.log('Loading students for class:', classId, 'month-year:', monthYear);

                    // Cancel previous request
                    HalaqahReportSystem.utils.cancelRequest('classStudents');

                    // Show loading
                    HalaqahReportSystem.ui.showDropdownLoading('#studentList');

                    const url = HalaqahReportSystem.config.routes.classStudents.replace('CLASS_ID', classId);
                    const request = $.ajax({
                        type: 'GET',
                        url: url,
                        data: monthYear ? { monthYear: monthYear } : {},
                        success: (data) => {
                            HalaqahReportSystem.state.activeRequests.delete('classStudents');
                            HalaqahReportSystem.ui.hideDropdownLoading('#studentList');

                            let options = '<option value="">👤 Select Students (Multiple)</option>';

                            if (!data || data.length === 0) {
                                options = '<option value="" disabled>No students found</option>';
                            } else {
                                data.forEach(student => {
                                    options += `<option value="${student.id}">${student.full_name}</option>`;
                                });
                            }

                            HalaqahReportSystem.ui.updateDropdownOptions('#studentList', options);

                            // Show/hide student action buttons
                            HalaqahReportSystem.ui.toggleStudentButtons(data && data.length > 0);

                            HalaqahReportSystem.utils.showNotification(
                                `<i class="fa fa-users"></i> Loaded ${data ? data.length : 0} students`,
                                'success',
                                3000
                            );
                        },
                        error: (xhr, status, error) => {
                            HalaqahReportSystem.state.activeRequests.delete('classStudents');
                            if (xhr.statusText !== 'abort') {
                                HalaqahReportSystem.ui.hideDropdownLoading('#studentList');

                                const errorMsg = HalaqahReportSystem.utils.formatError(xhr, 'Failed to load students');
                                HalaqahReportSystem.ui.updateDropdownOptions('#studentList', `<option value="">Error: ${errorMsg}</option>`);

                                HalaqahReportSystem.utils.showNotification(
                                    `<strong>Error:</strong> ${errorMsg}`,
                                    'error',
                                    8000
                                );
                            }
                        }
                    });

                    HalaqahReportSystem.state.activeRequests.set('classStudents', request);
                };

            // Load teachers for selected class
            HalaqahReportSystem.loadClassTeachers = function(classId) {
                const url = HalaqahReportSystem.config.routes.getTeachers.replace(':classId', classId);
                $.ajax({
                    url: url,
                    method: 'GET',
                    data: { classId: classId },
                    success: (response) => {
                        const teacherNames = response || [];
                        $('#classesList').data('custom-attribute', teacherNames.join(','));
                    },
                    error: (xhr, status, error) => {
                        console.warn('Failed to load teachers:', error);
                    }
                });
            };

            // Month-Year change handler
            HalaqahReportSystem.handleMonthYearChange = function() {
                const monthYear = $('#monthlyHalaqahReportMonthYearList').val();
                const monthName = $('#monthlyHalaqahReportMonthYearList option:selected').data('name');

                console.log('Month-Year changed:', monthYear, monthName);

                // Update state
                HalaqahReportSystem.filters.updateState('monthYear', monthYear);

                // Update UI elements
                $('.summaryMonth').text(monthName || '');

                // Note: We don't reload students when month changes (as per requirement #2)
                // Students selection is independent of month-year selection
            };

            // Student change handler (multi-select)
            HalaqahReportSystem.handleStudentChange = function() {
                const selectedStudents = $('#studentList').val() || [];

                console.log('Students changed:', selectedStudents);

                // Update state
                HalaqahReportSystem.filters.updateState('students', selectedStudents);

                // Note: We don't reload month-years when students change (as per requirement #2)
                // Month-year selection is independent of student selection
            };

            // ===================================================================
            // SIDE DRAWER PANEL FUNCTIONS
            // ===================================================================

            // Side drawer panel management
            HalaqahReportSystem.studentPanel = {
                isOpen: false,
                selectedStudents: [],
                allStudents: [],
                filteredStudents: [],
                searchTimeout: null
            };

            // Open student selection panel
            HalaqahReportSystem.openStudentPanel = function() {
                const classId = HalaqahReportSystem.state.filterValues.class;
                
                if (!classId) {
                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-warning"></i> Please select a class first',
                        'warning',
                        3000
                    );
                    return;
                }

                console.log('🎯 Opening student panel for class:', classId);
                
                $('#studentPanel').addClass('open');
                $('#studentPanelOverlay').addClass('active');
                HalaqahReportSystem.studentPanel.isOpen = true;
                
                // Load students for the panel
                HalaqahReportSystem.loadStudentsForPanel(classId);
                
                // Update current selections in panel
                HalaqahReportSystem.studentPanel.selectedStudents = [...HalaqahReportSystem.state.filterValues.students];
                HalaqahReportSystem.updatePanelSelectionCount();
            };

            // Close student selection panel
            HalaqahReportSystem.closeStudentPanel = function() {
                $('#studentPanel').removeClass('open');
                $('#studentPanelOverlay').removeClass('active');
                HalaqahReportSystem.studentPanel.isOpen = false;
                
                // Clear search
                $('#studentPanelSearch').val('');
            };

            // Load students for panel
            HalaqahReportSystem.loadStudentsForPanel = function(classId) {
                console.log('📚 Loading students for panel, class ID:', classId);
                
                // Show loading state
                $('#studentLoadingState').show();
                $('#studentPanelList').hide();
                $('#studentEmptyState').hide();
                
                // Cancel any existing request
                HalaqahReportSystem.utils.cancelRequest('panelStudents');
                
                const url = HalaqahReportSystem.config.routes.classStudents.replace('CLASS_ID', classId);
                
                const request = $.ajax({
                    url: url,
                    method: 'GET',
                    success: function(data) {
                        console.log('✅ Students loaded for panel:', data);
                        
                        HalaqahReportSystem.studentPanel.allStudents = data || [];
                        HalaqahReportSystem.studentPanel.filteredStudents = [...HalaqahReportSystem.studentPanel.allStudents];
                        
                        HalaqahReportSystem.renderStudentPanelList();
                        
                        // Hide loading state
                        $('#studentLoadingState').hide();
                        $('#studentPanelList').show();
                    },
                    error: function(xhr, status, error) {
                        console.error('❌ Failed to load students for panel:', error);
                        
                        $('#studentLoadingState').hide();
                        $('#studentEmptyState').show();
                        
                        const errorMsg = HalaqahReportSystem.utils.formatError(xhr, 'Failed to load students');
                        HalaqahReportSystem.utils.showNotification(
                            `<i class="fa fa-times-circle"></i> Error: ${errorMsg}`,
                            'error',
                            5000
                        );
                    }
                });
                
                HalaqahReportSystem.state.activeRequests.set('panelStudents', request);
            };

            // Render student list in panel
            HalaqahReportSystem.renderStudentPanelList = function(searchTerm = '') {
                const $list = $('#studentPanelList');
                const students = HalaqahReportSystem.studentPanel.filteredStudents;
                
                if (students.length === 0) {
                    $('#studentEmptyState').show();
                    $('#studentPanelList').hide();
                    return;
                }
                
                $('#studentEmptyState').hide();
                $('#studentPanelList').show();
                
                // Process students and get class names in parallel
                let html = '';
                const classIdToName = new Map(); // Cache for class names
                
                students.forEach(student => {
                    const isSelected = HalaqahReportSystem.studentPanel.selectedStudents.includes(student.id.toString());
                    
                    // Get student name and ID
                    let studentName = student.full_name || 'Unknown Student';
                    let studentId = student.id || '';
                    let classId = null;
                    
                    // Extract class ID from hefz plans
                    if (student.current_hefz_plan && student.current_hefz_plan.class_id) {
                        classId = student.current_hefz_plan.class_id;
                    }
                    
                    // Apply search highlighting
                    let displayName = studentName;
                    let displayId = studentId.toString();
                    
                    if (searchTerm.trim()) {
                        const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const regex = new RegExp(`(${escapedTerm})`, 'gi');
                        displayName = studentName.replace(regex, '<span class="search-highlight">$1</span>');
                        displayId = studentId.toString().replace(regex, '<span class="search-highlight">$1</span>');
                    }
                    
                    // Generate HTML with placeholders for dynamic content
                    html += `
                        <div class="student-panel-item ${isSelected ? 'selected' : ''}" data-student-id="${student.id}">
                            <img src="/student-image/${student.id}" alt="Student Photo" class="student-avatar" 
                                 onerror="this.src='/maleStudentProfilePicture.png'">
                            <div class="student-checkbox"></div>
                            <div class="student-info">
                                <div class="student-name">${displayName}</div>
                                <div class="student-meta">
                                    ID: ${displayId}
                                    <span class="class-name-placeholder" data-class-id="${classId || ''}">${classId ? ' • Loading...' : ''}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                $list.html(html);
                
                // Now fetch class names for all unique class IDs
                const uniqueClassIds = [...new Set(students
                    .filter(s => s.current_hefz_plan && s.current_hefz_plan.class_id)
                    .map(s => s.current_hefz_plan.class_id))];
                
                // Fetch class names in parallel
                const classPromises = uniqueClassIds.map(classId => {
                    return $.get(`/class-name/${classId}`)
                        .then(response => ({ id: classId, name: response.name }))
                        .catch(() => ({ id: classId, name: 'Unknown Class' }));
                });
                
                Promise.all(classPromises).then(classResults => {
                    // Update the DOM with class names
                    classResults.forEach(classInfo => {
                        $(`.class-name-placeholder[data-class-id="${classInfo.id}"]`)
                            .text(` • ${classInfo.name}`);
                    });
                });
                
                HalaqahReportSystem.updatePanelSelectionCount();
            };

            // Toggle student selection in panel
            HalaqahReportSystem.toggleStudentInPanel = function(studentId) {
                const index = HalaqahReportSystem.studentPanel.selectedStudents.indexOf(studentId);
                
                if (index === -1) {
                    HalaqahReportSystem.studentPanel.selectedStudents.push(studentId);
                } else {
                    HalaqahReportSystem.studentPanel.selectedStudents.splice(index, 1);
                }
                
                // Update UI
                const $item = $(`.student-panel-item[data-student-id="${studentId}"]`);
                $item.toggleClass('selected');
                
                HalaqahReportSystem.updatePanelSelectionCount();
            };

            // Update selection count in panel
            HalaqahReportSystem.updatePanelSelectionCount = function() {
                const count = HalaqahReportSystem.studentPanel.selectedStudents.length;
                $('#studentPanelSelectedCount').text(count);
                
                // Update main filter badge
                $('#studentCountBadge').text(count);
                
                if (count > 0) {
                    $('#studentCountBadge').addClass('has-selection');
                    $('#studentSelectionText').text(`${count} student${count !== 1 ? 's' : ''} selected`);
                    $('.student-filter-card').addClass('active');
                    $('#studentStatus .fa').removeClass('fa-circle-o').addClass('fa-check-circle');
                } else {
                    $('#studentCountBadge').removeClass('has-selection');
                    $('#studentSelectionText').text('Select students');
                    $('.student-filter-card').removeClass('active');
                    $('#studentStatus .fa').removeClass('fa-check-circle').addClass('fa-circle-o');
                }
            };

            // Apply student selection from panel
            HalaqahReportSystem.applyStudentSelection = function() {
                console.log('✅ Applying student selection:', HalaqahReportSystem.studentPanel.selectedStudents);
                
                // Update main state
                HalaqahReportSystem.filters.updateState('students', HalaqahReportSystem.studentPanel.selectedStudents);
                
                // Close panel
                HalaqahReportSystem.closeStudentPanel();
                
                // Show notification
                const count = HalaqahReportSystem.studentPanel.selectedStudents.length;
                if (count > 0) {
                    HalaqahReportSystem.utils.showNotification(
                        `<i class="fa fa-check-circle"></i> ${count} student${count !== 1 ? 's' : ''} selected for report`,
                        'success',
                        3000
                    );
                }
            };

            // Clear student selection in panel
            HalaqahReportSystem.clearStudentSelection = function() {
                HalaqahReportSystem.studentPanel.selectedStudents = [];
                $('.student-panel-item').removeClass('selected');
                HalaqahReportSystem.updatePanelSelectionCount();
            };

            // Search students in panel
            HalaqahReportSystem.searchStudentsInPanel = function(searchTerm) {
                const allStudents = HalaqahReportSystem.studentPanel.allStudents;
                
                if (!searchTerm.trim()) {
                    HalaqahReportSystem.studentPanel.filteredStudents = [...allStudents];
                } else {
                    const term = searchTerm.toLowerCase();
                    HalaqahReportSystem.studentPanel.filteredStudents = allStudents.filter(student => {
                        const fullName = (student.full_name || '').toLowerCase();
                        const studentId = (student.id || '').toString().toLowerCase();
                        
                        return fullName.includes(term) || studentId.includes(term);
                    });
                }
                
                HalaqahReportSystem.renderStudentPanelList(searchTerm);
            };

            // Excel export handler
            HalaqahReportSystem.handleExcelExport = function(e) {
                e.preventDefault();

                const state = HalaqahReportSystem.state.filterValues;

                if (!state.class || !state.monthYear) {
                    HalaqahReportSystem.ui.showValidationMessage(
                        'Please select both Class and Month-Year before exporting.',
                        'warning'
                    );
                    return;
                }

                console.log('Exporting Excel with filters:', state);

                // Build export URL
                let exportUrl = HalaqahReportSystem.config.routes.exportExcel.replace('CLASS_ID', state.class);
                exportUrl += `?monthYear=${encodeURIComponent(state.monthYear)}`;

                if (state.students.length > 0) {
                    exportUrl += `&studentId=${state.students.join(',')}`;
                }
                if (state.center) {
                    exportUrl += `&centerId=${state.center}`;
                }

                console.log('Export URL:', exportUrl);

                // Show loading state
                const $exportBtn = $('#exportExcelButton');
                const originalHtml = $exportBtn.html();
                $exportBtn.html('<i class="fa fa-spinner fa-spin"></i> Exporting...').prop('disabled', true);

                // Direct download approach
                window.location.href = exportUrl;

                // Reset button after delay and hide progress modal
                setTimeout(() => {
                    $exportBtn.html(originalHtml).prop('disabled', false);

                    // Hide progress modal if it was shown
                    HalaqahReportSystem.ui.hideExportProgressModal();

                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-download"></i> Excel export completed',
                        'success',
                        3000
                    );
                }, 2000);
            };
          

            // ===================================================================
            // INITIAL SETUP
            // ===================================================================

            // Initial setup when document is ready
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // The ajaxSend() method is used to execute some code just before making an Ajax request
                $(document).ajaxSend(function () {
                    $("#formLoader").addClass("ui loading form");
                });

                // The ajaxComplete() method is used to execute some code when an Ajax request completes
                $(document).ajaxComplete(function () {
                    $("#formLoader").removeClass("ui loading form");
                });
            });

            // ===================================================================
            // ENHANCED PROGRESS BAR SYSTEM
            // ===================================================================

            HalaqahReportSystem.progressBars = {
                // Initialize all progress bars with enhanced styling
                initialize: function() {
                    console.log('🎨 Initializing Enhanced Progress Bar System...');
                    this.addProgressLegend();
                    this.enhanceExistingProgressBars();
                    this.bindProgressBarEvents();
                },

                // Add progress legend to the page
                addProgressLegend: function() {
                    const legendHtml = `
                        <div class="progress-legend">
                            <div class="legend-item">
                                <div class="legend-color legend-excellent"></div>
                                <span><strong>Excellent:</strong> 75% - 100% (Green)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-good"></div>
                                <span><strong>Good:</strong> 50% - 74.99% (Yellow/Orange)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-poor"></div>
                                <span><strong>Needs Improvement:</strong> 0.01% - 49.99% (Red)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-zero"></div>
                                <span><strong>No Data:</strong> 0% (Gray)</span>
                            </div>
                            <div style="margin-left: 16px; font-size: 11px; color: #6b7280; font-style: italic;">
                                💡 Click on any percentage bar for calculation details
                            </div>
                        </div>
                    `;

                    // Add legend before the first table
                    if ($('.progress-legend').length === 0) {
                        $('.table-responsive').first().before(legendHtml);
                    }
                },

                // Enhance existing progress bars
                enhanceExistingProgressBars: function() {
                    const self = this;

                    // Find all existing progress bars and enhance them
                    $('.progress').each(function() {
                        const $progressContainer = $(this);
                        const $progressBar = $progressContainer.find('.progress-bar');

                        if ($progressBar.length > 0) {
                            self.enhanceProgressBar($progressContainer, $progressBar);
                        }
                    });
                },

                // Enhance individual progress bar
                enhanceProgressBar: function($container, $bar) {
                    // Extract percentage from aria-valuenow or text content
                    let percentage = parseFloat($bar.attr('aria-valuenow')) || 0;

                    // If no aria-valuenow, try to extract from text
                    if (percentage === 0) {
                        const text = $bar.text() || $container.text();
                        const match = text.match(/(\d+(?:\.\d+)?)/);
                        if (match) {
                            percentage = parseFloat(match[1]);
                        }
                    }

                    // Determine color class based on percentage
                    const colorClass = this.getColorClass(percentage);

                    // Create enhanced progress bar HTML
                    const enhancedHtml = `
                        <div class="enhanced-progress-container ${colorClass}"
                             data-tooltip="Achievement: ${percentage.toFixed(1)}%">
                            <div class="enhanced-progress-fill" style="width: ${percentage}%"></div>
                            <div class="enhanced-progress-text">${percentage.toFixed(1)}%</div>
                        </div>
                    `;

                    // Replace the old progress bar
                    $container.replaceWith(enhancedHtml);
                },

                // Get color class based on percentage
                getColorClass: function(percentage) {
                    if (percentage === 0) return 'progress-zero';
                    if (percentage < 50) return 'progress-poor';
                    if (percentage < 75) return 'progress-good';
                    return 'progress-excellent';
                },

                // Create new progress bar HTML
                createProgressBar: function(percentage, tooltip = null) {
                    const colorClass = this.getColorClass(percentage);
                    const tooltipAttr = tooltip ? `data-tooltip="${tooltip}"` : `data-tooltip="Achievement: ${percentage.toFixed(1)}%"`;

                    return `
                        <div class="enhanced-progress-container ${colorClass}" ${tooltipAttr}>
                            <div class="enhanced-progress-fill" style="width: ${percentage}%"></div>
                            <div class="enhanced-progress-text">${percentage.toFixed(1)}%</div>
                        </div>
                    `;
                },

                // Bind progress bar events
                bindProgressBarEvents: function() {
                    // Add click event for detailed calculation explanation
                    $(document).on('click', '.enhanced-progress-container', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const $container = $(this);
                        const percentage = parseFloat($container.find('.enhanced-progress-text').text()) || 0;
                        const tooltip = $container.attr('data-tooltip') || '';
                        const calculationType = $container.attr('data-calculation-type') || 'general';

                        HalaqahReportSystem.progressBars.showCalculationDetails(percentage, calculationType, tooltip, $container);
                    });

                    // Add hover effects
                    $(document).on('mouseenter', '.enhanced-progress-container', function() {
                        $(this).css('transform', 'translateY(-2px) scale(1.02)');
                    });

                    $(document).on('mouseleave', '.enhanced-progress-container', function() {
                        $(this).css('transform', 'translateY(0) scale(1)');
                    });
                },

                // Show detailed calculation explanation
                showCalculationDetails: function(percentage, calculationType, tooltip, $container) {
                    let title = '';
                    let content = '';
                    let icon = 'info';

                    // Determine color and icon based on percentage
                    if (percentage >= 75) {
                        icon = 'success';
                    } else if (percentage >= 50) {
                        icon = 'warning';
                    } else if (percentage > 0) {
                        icon = 'error';
                    } else {
                        icon = 'question';
                    }

                    switch (calculationType) {
                        case 'attendance':
                            title = '📊 Attendance Percentage Calculation';
                            content = `
                                <div style="text-align: left; line-height: 1.6;">
                                    <h4 style="color: #1f2937; margin-bottom: 16px;">How is this calculated?</h4>

                                    <div style="background: #f8fafc; padding: 12px; border-radius: 8px; margin-bottom: 16px;">
                                        <strong>Formula:</strong><br>
                                        <code style="background: #e2e8f0; padding: 4px 8px; border-radius: 4px;">
                                            (Present Days ÷ Total Class Days) × 100
                                        </code>
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Current Result:</strong> ${percentage.toFixed(2)}%
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Performance Level:</strong><br>
                                        ${this.getPerformanceDescription(percentage)}
                                    </div>

                                    <div style="background: #fef3c7; padding: 12px; border-radius: 8px; border-left: 4px solid #f59e0b;">
                                        <strong>💡 Note:</strong> This percentage reflects the student's attendance consistency for the selected month/period.
                                    </div>
                                </div>
                            `;
                            break;

                        case 'hefz_achievement':
                            title = '📖 Memorization Achievement Calculation';
                            content = `
                                <div style="text-align: left; line-height: 1.6;">
                                    <h4 style="color: #1f2937; margin-bottom: 16px;">How is this calculated?</h4>

                                    <div style="background: #f8fafc; padding: 12px; border-radius: 8px; margin-bottom: 16px;">
                                        <strong>Formula:</strong><br>
                                        <code style="background: #e2e8f0; padding: 4px 8px; border-radius: 4px;">
                                            (Memorized Pages ÷ Planned Pages) × 100
                                        </code>
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Current Result:</strong> ${percentage.toFixed(2)}%
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Calculation Process:</strong>
                                        <ol style="margin: 8px 0; padding-left: 20px;">
                                            <li>Count total pages memorized from reports</li>
                                            <li>Count total pages planned from student's hefz plan</li>
                                            <li>Calculate percentage: memorized ÷ planned × 100</li>
                                            <li>Cap at 100% maximum</li>
                                        </ol>
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Performance Level:</strong><br>
                                        ${this.getPerformanceDescription(percentage)}
                                    </div>

                                    <div style="background: #dcfce7; padding: 12px; border-radius: 8px; border-left: 4px solid #10b981;">
                                        <strong>📚 Note:</strong> This shows how well students are meeting their memorization goals compared to their individual plans.
                                    </div>
                                </div>
                            `;
                            break;

                        case 'revision_achievement':
                            title = '🔄 Revision Achievement Calculation';
                            content = `
                                <div style="text-align: left; line-height: 1.6;">
                                    <h4 style="color: #1f2937; margin-bottom: 16px;">How is this calculated?</h4>

                                    <div style="background: #f8fafc; padding: 12px; border-radius: 8px; margin-bottom: 16px;">
                                        <strong>Formula:</strong><br>
                                        <code style="background: #e2e8f0; padding: 4px 8px; border-radius: 4px;">
                                            (Revised Pages ÷ Planned Revision Pages) × 100
                                        </code>
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Current Result:</strong> ${percentage.toFixed(2)}%
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Calculation Process:</strong>
                                        <ol style="margin: 8px 0; padding-left: 20px;">
                                            <li>Count total pages revised from revision reports</li>
                                            <li>Count total pages planned for revision</li>
                                            <li>Calculate percentage: revised ÷ planned × 100</li>
                                            <li>Consider study direction (forward/backward)</li>
                                        </ol>
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Performance Level:</strong><br>
                                        ${this.getPerformanceDescription(percentage)}
                                    </div>

                                    <div style="background: #fef3c7; padding: 12px; border-radius: 8px; border-left: 4px solid #f59e0b;">
                                        <strong>🔄 Note:</strong> This measures how effectively students are reviewing previously memorized content.
                                    </div>
                                </div>
                            `;
                            break;

                        default:
                            title = '📊 Performance Calculation';
                            content = `
                                <div style="text-align: left; line-height: 1.6;">
                                    <h4 style="color: #1f2937; margin-bottom: 16px;">Performance Details</h4>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Current Result:</strong> ${percentage.toFixed(2)}%
                                    </div>

                                    <div style="margin-bottom: 16px;">
                                        <strong>Performance Level:</strong><br>
                                        ${this.getPerformanceDescription(percentage)}
                                    </div>

                                    <div style="background: #f3f4f6; padding: 12px; border-radius: 8px;">
                                        <strong>ℹ️ Note:</strong> This percentage represents the calculated performance metric for this specific data point.
                                    </div>
                                </div>
                            `;
                    }

                    // Show SweetAlert with calculation details
                    Swal.fire({
                        title: title,
                        html: content,
                        icon: icon,
                        width: '600px',
                        showCloseButton: true,
                        showCancelButton: false,
                        confirmButtonText: 'Got it!',
                        confirmButtonColor: percentage >= 75 ? '#10b981' : percentage >= 50 ? '#f59e0b' : '#ef4444',
                        customClass: {
                            popup: 'calculation-details-popup',
                            title: 'calculation-details-title',
                            content: 'calculation-details-content'
                        }
                    });
                },

                // Get performance description based on percentage
                getPerformanceDescription: function(percentage) {
                    if (percentage >= 75) {
                        return '<span style="color: #10b981; font-weight: 600;">🟢 Excellent Performance (75% - 100%)</span><br>Student is exceeding expectations and maintaining high standards.';
                    } else if (percentage >= 50) {
                        return '<span style="color: #f59e0b; font-weight: 600;">🟡 Good Performance (50% - 74.99%)</span><br>Student is meeting most requirements with room for improvement.';
                    } else if (percentage > 0) {
                        return '<span style="color: #ef4444; font-weight: 600;">🔴 Needs Improvement (0.01% - 49.99%)</span><br>Student requires additional support and attention to meet goals.';
                    } else {
                        return '<span style="color: #6b7280; font-weight: 600;">⚫ No Data Available (0%)</span><br>No records found for this period or metric.';
                    }
                },

                // Update progress bar with animation
                updateProgressBar: function($container, newPercentage, animate = true) {
                    const $fill = $container.find('.enhanced-progress-fill');
                    const $text = $container.find('.enhanced-progress-text');
                    const colorClass = this.getColorClass(newPercentage);

                    // Update color class
                    $container.removeClass('progress-excellent progress-good progress-poor progress-zero')
                              .addClass(colorClass);

                    // Update tooltip
                    $container.attr('data-tooltip', `Achievement: ${newPercentage.toFixed(1)}%`);

                    if (animate) {
                        // Animate the progress bar
                        $fill.animate({
                            width: newPercentage + '%'
                        }, 800, 'easeOutCubic');

                        // Animate the text
                        $({ percentage: parseFloat($text.text()) }).animate({
                            percentage: newPercentage
                        }, {
                            duration: 800,
                            easing: 'easeOutCubic',
                            step: function() {
                                $text.text(this.percentage.toFixed(1) + '%');
                            }
                        });
                    } else {
                        $fill.css('width', newPercentage + '%');
                        $text.text(newPercentage.toFixed(1) + '%');
                    }
                }
            };

            // ===================================================================
            // ENHANCED DATATABLES SYSTEM
            // ===================================================================

            HalaqahReportSystem.dataTables = {
                // Initialize enhanced DataTables
                initialize: function() {
                    console.log('📊 Initializing Enhanced DataTables System...');
                    this.enhanceExistingTables();
                    this.bindTableEvents();
                    this.addTableEnhancements();
                },

                // Enhance existing DataTables
                enhanceExistingTables: function() {
                    $('.table').each(function() {
                        const $table = $(this);

                        // Add enhanced classes
                        $table.addClass('table-enhanced');

                        // Enhance wrapper if it exists
                        const $wrapper = $table.closest('.dataTables_wrapper');
                        if ($wrapper.length > 0) {
                            $wrapper.addClass('enhanced-wrapper');
                        }
                    });
                },

                // Add table enhancements
                addTableEnhancements: function() {
                    // Add loading overlay for AJAX tables
                    $('.dataTables_wrapper').each(function() {
                        const $wrapper = $(this);
                        if ($wrapper.find('.loading-overlay').length === 0) {
                            $wrapper.append(`
                                <div class="loading-overlay" style="display: none;">
                                    <div class="loading-spinner">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="sr-only">Loading...</span>
                                        </div>
                                        <div class="loading-text">Loading data...</div>
                                    </div>
                                </div>
                            `);
                        }
                    });

                    // Enhance pagination buttons
                    this.enhancePagination();
                },

                // Enhance pagination
                enhancePagination: function() {
                    $(document).on('draw.dt', '.table', function() {
                        const $table = $(this);
                        const $wrapper = $table.closest('.dataTables_wrapper');

                        // Add icons to pagination buttons
                        $wrapper.find('.paginate_button.previous').html('<i class="fa fa-chevron-left"></i> Previous');
                        $wrapper.find('.paginate_button.next').html('Next <i class="fa fa-chevron-right"></i>');

                        // Add tooltips to pagination buttons
                        $wrapper.find('.paginate_button').each(function() {
                            const $btn = $(this);
                            if ($btn.hasClass('current')) {
                                $btn.attr('title', 'Current page');
                            } else if ($btn.hasClass('disabled')) {
                                $btn.attr('title', 'Not available');
                            } else {
                                $btn.attr('title', 'Go to page ' + $btn.text());
                            }
                        });
                    });
                },

                // Bind table events
                bindTableEvents: function() {
                    // Show loading overlay during AJAX requests
                    $(document).on('preXhr.dt', '.table', function() {
                        $(this).closest('.dataTables_wrapper').find('.loading-overlay').fadeIn(200);
                    });

                    $(document).on('xhr.dt', '.table', function() {
                        $(this).closest('.dataTables_wrapper').find('.loading-overlay').fadeOut(200);
                    });

                    // Add row click effects
                    $(document).on('click', '.table-enhanced tbody tr', function(e) {
                        // Don't trigger if clicking on interactive elements
                        if ($(e.target).is('a, button, input, select, .enhanced-progress-container')) {
                            return;
                        }

                        const $row = $(this);

                        // Add click effect
                        $row.addClass('row-clicked');
                        setTimeout(() => {
                            $row.removeClass('row-clicked');
                        }, 300);

                        // Optional: Show row details
                        this.showRowDetails($row);
                    }.bind(this));

                    // Add column sorting enhancements
                    $(document).on('click', '.table-enhanced thead th', function() {
                        const $th = $(this);

                        // Add sorting animation
                        $th.addClass('sorting-active');
                        setTimeout(() => {
                            $th.removeClass('sorting-active');
                        }, 500);
                    });
                },

                // Show row details (optional feature)
                showRowDetails: function($row) {
                    // This can be customized based on specific needs
                    const rowData = [];
                    $row.find('td').each(function(index) {
                        const $cell = $(this);
                        const headerText = $row.closest('table').find('thead th').eq(index).text().trim();
                        const cellText = $cell.text().trim();

                        if (headerText && cellText) {
                            rowData.push(`<strong>${headerText}:</strong> ${cellText}`);
                        }
                    });

                    if (rowData.length > 0) {
                        HalaqahReportSystem.utils.showNotification(
                            `<div style="text-align: left;">${rowData.slice(0, 3).join('<br>')}</div>`,
                            'info',
                            4000
                        );
                    }
                },

                // Refresh table data
                refreshTable: function(tableSelector) {
                    const $table = $(tableSelector);
                    if ($table.length > 0 && $.fn.DataTable.isDataTable($table)) {
                        $table.DataTable().ajax.reload(null, false);
                    }
                },

                // Get table data
                getTableData: function(tableSelector) {
                    const $table = $(tableSelector);
                    if ($table.length > 0 && $.fn.DataTable.isDataTable($table)) {
                        return $table.DataTable().data().toArray();
                    }
                    return [];
                }
            };

            // ===================================================================
            // SYSTEM INITIALIZATION
            // ===================================================================

            // Initialize the entire system when document is ready
            $(document).ready(function() {
                console.log('🚀 Initializing Modern Halaqah Report System...');

                // Initialize CSRF token for AJAX requests
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Legacy Select2 initialization removed to prevent conflicts
                // All Select2 initialization is now handled by HalaqahReportSystem.initializeSelect2()

                // Initialize Semantic UI dropdowns (if any)
                $('.ui.dropdown').dropdown({
                    clearable: true
                });

                // Duplicate event system removed - using single HalaqahReportSystem.bindEventHandlers instead

                // Initialize filter state from current form values
                HalaqahReportSystem.filters.updateState('center', $('#monthlyHalaqahReportCenterList').val() || []);
                HalaqahReportSystem.filters.updateState('class', $('#classesList').val() || []);
                HalaqahReportSystem.filters.updateState('monthYear', $('#monthlyHalaqahReportMonthYearList').val());
                HalaqahReportSystem.filters.updateState('students', $('#studentList').val() || []);

                // Add enhanced CSS classes to tables
                $('.table').addClass('table-enhanced');

                // Initialize Enhanced Progress Bar System
                HalaqahReportSystem.progressBars.initialize();

                // Initialize Enhanced DataTables
                HalaqahReportSystem.dataTables.initialize();

                // Add loading animation styles
                $('<style>')
                    .prop('type', 'text/css')
                    .html(`
                        @keyframes slideInDown {
                            from {
                                transform: translate3d(0, -100%, 0);
                                visibility: visible;
                            }
                            to {
                                transform: translate3d(0, 0, 0);
                            }
                        }

                        .table-hover-effect {
                            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%) !important;
                            transform: scale(1.01);
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                            transition: all 0.2s ease;
                        }

                        .notification-enhanced {
                            animation: slideInDown 0.3s ease;
                        }

                        .btn-enhanced:active {
                            transform: translateY(0) scale(0.98);
                        }

                        /* ===================================================================
                           MINIMAL DATATABLE FIXES - PRODUCTION READY
                           =================================================================== */

                        /* Fix percentage text wrapping */
                        .dataTable td {
                            white-space: nowrap !important;
                        }

                        /* Ensure proper column alignment */
                        .dataTable .text-center {
                            text-align: center !important;
                        }

                        .dataTable .text-left {
                            text-align: left !important;
                        }

















                        .filter-loading::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: rgba(255, 255, 255, 0.8);
                            z-index: 1000;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .filter-loading::before {
                            content: '';
                            width: 40px;
                            height: 40px;
                            border: 4px solid #f3f3f3;
                            border-top: 4px solid #28a745;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            z-index: 1001;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            margin: -20px 0 0 -20px;
                        }
                    `)
                    .appendTo('head');

                // Show welcome message
                setTimeout(() => {
                    HalaqahReportSystem.utils.showNotification(
                        '<i class="fa fa-rocket"></i> <strong>Modern Halaqah Report System</strong> initialized successfully!<br><small>Enhanced with smooth animations, multi-select students, and improved error handling.</small>',
                        'success',
                        8000
                    );
                }, 1000);

                console.log('✅ Modern Halaqah Report System initialized successfully!');
            });

        </script>

        <!-- Student Selection Panel - Side Drawer -->
        <div class="student-selection-panel" id="studentPanel">
            <div class="student-panel-header">
                <div class="student-panel-title">
                    <h4>Student Selection</h4>
                    <button class="student-panel-close" id="closeStudentPanel">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="student-panel-subtitle">Select students for your report</div>
            </div>
            
            <div class="student-panel-body">
                <div class="student-search-box">
                    <i class="fa fa-search student-search-icon"></i>
                    <input type="text" class="student-search-input" id="studentPanelSearch" placeholder="Search students by name or ID...">
                    <i class="fa fa-times student-search-clear" id="studentSearchClear"></i>
                </div>
                
                <div class="student-panel-list" id="studentPanelList">
                    <!-- Student items will be populated here -->
                </div>
                
                <div class="student-empty-state" id="studentEmptyState" style="display: none;">
                    <div class="student-empty-icon">
                        <i class="fa fa-users-slash"></i>
                    </div>
                    <h4 class="student-empty-title">No students found</h4>
                    <p class="student-empty-description">Try adjusting your search or filter criteria to find students</p>
                </div>
                
                <div class="student-loading-state" id="studentLoadingState" style="display: none;">
                    <div class="student-loading-spinner"></div>
                    <div class="student-loading-text">Loading students...</div>
                </div>
            </div>
            
            <div class="student-panel-summary">
                <div class="student-summary-content">
                    <div class="student-selected-count">
                        <span id="studentPanelSelectedCount">0</span> students selected
                    </div>
                    <div class="student-action-buttons">
                        <button class="student-btn student-btn-outline" id="clearStudentSelection">Clear</button>
                        <button class="student-btn student-btn-primary" id="applyStudentSelection">Apply</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Panel Overlay -->
        <div class="student-panel-overlay" id="studentPanelOverlay"></div>

    <?php $__env->appendSection(); ?>


<?php echo $__env->make('layouts.hound', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\itqan\resources\views/modules/educationalreports/reports/halaqah.blade.php ENDPATH**/ ?>