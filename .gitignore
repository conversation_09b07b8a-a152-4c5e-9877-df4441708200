# Official Laravel .gitignore (based on <PERSON><PERSON> 12.x)
*.log
.DS_Store
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
/.fleet
/.idea
/.nova
/.phpunit.cache
/.vscode
/.zed
/auth.json
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/logs/*
/vendor
Homestead.json
Homestead.yaml
Thumbs.db

# Project-specific additions
/storage/dbBackup/*
/planing/*
/storage/debugbar/*

# User-uploaded files (CRITICAL: These should never be tracked)
/public/uploads/*
/public/userfiles/*
/public/students/*
/public/photos/*
/public/files/*
/public/org_logos/*
/public/images/employees/*
/public/images/students/*
/public/temp/*

# Keep placeholder/template files but ignore user content
!/public/images/employees/.gitkeep
!/public/images/students/.gitkeep
!/public/uploads/.gitkeep
!/public/userfiles/.gitkeep
!/public/students/.gitkeep
!/public/photos/.gitkeep
!/public/temp/.gitkeep

# Keep important directories but ignore contents
!/storage/tmp/
/storage/tmp/*
!/storage/tmp/.gitkeep